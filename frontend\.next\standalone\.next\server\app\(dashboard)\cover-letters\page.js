(()=>{var e={};e.id=498,e.ids=[498],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},251:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(1141),r(2626),r(546);var s=r(170),a=r(5002),o=r(3876),l=r.n(o),n=r(6299),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let d=["",{children:["(dashboard)",{children:["cover-letters",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1141)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\cover-letters\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\cover-letters\\page.tsx"],u="/(dashboard)/cover-letters/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/cover-letters/page",pathname:"/cover-letters",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6403:(e,t,r)=>{Promise.resolve().then(r.bind(r,4652))},4652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C,dynamic:()=>y});var s=r(7247),a=r(8964),o=r(7734),l=r(9767),n=r(5503),i=r(223),d=r(8053),c=r(9379),u=r(8749),p=r(8339),m=r(4615),x=r(5497),h=r(4445),v=r(906),g=r(7757),f=r(6991),j=r(4049);let y="force-dynamic";function w({count:e,isLoading:t}){return(0,s.jsxs)(g.Zb,{className:"overflow-hidden border-muted-foreground/20",children:[(0,s.jsxs)(g.Ol,{className:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 pb-2",children:[(0,s.jsxs)(g.ll,{className:"flex items-center gap-2 text-purple-700 dark:text-purple-300",children:[s.jsx(c.Z,{className:"h-5 w-5"}),"Cover Letters"]}),s.jsx(g.SZ,{children:"Your job applications"})]}),s.jsx(g.aY,{className:"pt-4",children:t?s.jsx("div",{className:"h-8 w-16 animate-pulse rounded-md bg-muted"}):s.jsx("div",{className:"text-2xl font-bold",children:e})})]})}function C(){let{isAuthenticated:e,loading:t}=(0,o.a)(),[r,g]=(0,a.useState)(!1),[y,C]=(0,a.useState)([]),[b,A]=(0,a.useState)(!0),[N,P]=(0,a.useState)(!1),[D,L]=(0,a.useState)(""),[q,S]=(0,a.useState)("newest"),{toast:_}=(0,v.pm)(),I=async()=>{A(!0),P(!0);try{let e=await h.Gi.getAllCoverLetters();console.log("Cover letter API response:",e);let t=[];e.data.coverLetters&&Array.isArray(e.data.coverLetters)?(console.log("Using coverLetters array from response"),t=e.data.coverLetters):e.data.data&&Array.isArray(e.data.data)?(console.log("Using data array from response"),t=e.data.data):Array.isArray(e.data)?(console.log("Response.data is directly an array"),t=e.data):console.warn("Could not find cover letter data in response:",e),console.log("Cover letter data to be set:",t),C(t)}catch(e){console.error("Error fetching cover letters:",e),_({title:"Error fetching cover letters",description:"Please try again later",variant:"destructive"})}finally{A(!1),P(!1)}},T=()=>{g(!0)},k=async e=>{try{await h.Gi.deleteCoverLetter(e),C(y.filter(t=>t.id!==e)),_({title:"Cover letter deleted",description:"Your cover letter has been deleted successfully"})}catch(e){_({title:"Error deleting cover letter",description:"Please try again later",variant:"destructive"})}},Z=[...y.filter(e=>e.title.toLowerCase().includes(D.toLowerCase())||e.jobTitle&&e.jobTitle.toLowerCase().includes(D.toLowerCase())||e.company&&e.company.toLowerCase().includes(D.toLowerCase()))].sort((e,t)=>{try{switch(q){case"newest":let r=e.updatedAt?new Date(e.updatedAt).getTime():new Date(e.createdAt).getTime();return(t.updatedAt?new Date(t.updatedAt).getTime():new Date(t.createdAt).getTime())-r;case"oldest":let s=e.updatedAt?new Date(e.updatedAt).getTime():new Date(e.createdAt).getTime(),a=t.updatedAt?new Date(t.updatedAt).getTime():new Date(t.createdAt).getTime();return s-a;case"a-z":return(e.title||"").localeCompare(t.title||"");case"z-a":return(t.title||"").localeCompare(e.title||"");default:return 0}}catch(e){return console.error("Error sorting cover letters:",e),0}});return t?s.jsx("div",{className:"flex h-screen items-center justify-center",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[s.jsx(u.Z,{className:"h-10 w-10 animate-spin text-primary"}),s.jsx("p",{className:"text-sm text-muted-foreground animate-pulse",children:"Loading your cover letters..."})]})}):(0,s.jsxs)(l.r,{children:[s.jsx(n.x,{heading:"Cover Letters",text:"Create and manage your job application cover letters",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(d.z,{variant:"outline",size:"icon",onClick:I,disabled:N,className:"h-9 w-9",children:[s.jsx(p.Z,{className:`h-4 w-4 ${N?"animate-spin":""}`}),s.jsx("span",{className:"sr-only",children:"Refresh"})]}),(0,s.jsxs)(d.z,{onClick:T,className:"gap-1",children:[s.jsx(m.Z,{className:"mr-1 h-4 w-4"}),"New Cover Letter"]})]})}),s.jsx("div",{className:"mt-8",children:s.jsx(w,{count:y.length,isLoading:b})}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:justify-between mt-8",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-96",children:[s.jsx(f.I,{placeholder:"Search cover letters...",value:D,onChange:e=>L(e.target.value),className:"pl-10"}),s.jsx(c.Z,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,s.jsxs)(j.Ph,{value:q,onValueChange:e=>{console.log("Changing sort order to:",e),S(e)},children:[s.jsx(j.i4,{className:"w-[180px]",children:s.jsx(j.ki,{placeholder:"Sort order"})}),(0,s.jsxs)(j.Bw,{children:[s.jsx(j.Ql,{value:"newest",children:"Newest first"}),s.jsx(j.Ql,{value:"oldest",children:"Oldest first"}),s.jsx(j.Ql,{value:"a-z",children:"A to Z"}),s.jsx(j.Ql,{value:"z-a",children:"Z to A"})]})]})]})]}),s.jsx("div",{className:"mt-8",children:s.jsx(i.R,{type:"coverLetter",documents:Z,loading:b,onCreateNew:T,onDelete:k,onRefresh:I,hideSearch:!0,hideSort:!0,externalSearchQuery:D})}),s.jsx("div",{className:"mt-6",children:s.jsx(x.g,{open:r,onOpenChange:g,documentType:"coverLetter",onDocumentCreated:()=>{I(),g(!1),_({title:"Cover letter created",description:"Your cover letter has been created successfully",key:"cover-letter-created-"+new Date().getTime()})}})})]})}},1141:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>a});var s=r(5347);let a=(0,s.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\cover-letters\page.tsx#dynamic`),o=(0,s.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\cover-letters\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[475,97,12,166,43,967,445,223],()=>r(251));module.exports=s})();