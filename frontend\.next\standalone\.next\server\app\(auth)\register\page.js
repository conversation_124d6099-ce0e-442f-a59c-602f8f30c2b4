(()=>{var e={};e.id=566,e.ids=[566],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},3779:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l}),t(2238),t(2626),t(546);var s=t(170),a=t(5002),i=t(3876),o=t.n(i),n=t(6299),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l=["",{children:["(auth)",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2238)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(auth)\\register\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(auth)\\register\\page.tsx"],u="/(auth)/register/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(auth)/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2709:(e,r,t)=>{Promise.resolve().then(t.bind(t,3030))},3030:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f,dynamic:()=>p});var s=t(7247),a=t(8964),i=t(9906),o=t(7734),n=t(8053),d=t(6991),l=t(2394),c=t(7757),u=t(2310),m=t(8749);let p="force-dynamic";function f(){let[e,r]=(0,a.useState)(""),[t,p]=(0,a.useState)(""),[f,x]=(0,a.useState)(""),[h,g]=(0,a.useState)(""),[v,b]=(0,a.useState)(""),{register:y,loading:w,error:j}=(0,o.a)(),N=async r=>{if(r.preventDefault(),f!==h){b("Passwords do not match");return}b(""),await y(e,t,f)};return(0,s.jsxs)("div",{className:"flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4",children:[(0,s.jsxs)(i.default,{href:"/",className:"mb-8 flex items-center gap-2",children:[s.jsx(u.Z,{className:"h-6 w-6"}),s.jsx("span",{className:"text-xl font-bold",children:"CareerPilotAI"})]}),(0,s.jsxs)(c.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(c.Ol,{className:"space-y-1",children:[s.jsx(c.ll,{className:"text-2xl font-bold",children:"Create an account"}),s.jsx(c.SZ,{children:"Enter your information to create an account"})]}),(0,s.jsxs)("form",{onSubmit:N,children:[(0,s.jsxs)(c.aY,{className:"space-y-4",children:[j&&s.jsx("div",{className:"rounded-md bg-destructive/15 p-3 text-sm text-destructive",children:j}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(l._,{htmlFor:"name",children:"Full Name"}),s.jsx(d.I,{id:"name",placeholder:"John Doe",value:e,onChange:e=>r(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(l._,{htmlFor:"email",children:"Email"}),s.jsx(d.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:t,onChange:e=>p(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(l._,{htmlFor:"password",children:"Password"}),s.jsx(d.I,{id:"password",type:"password",value:f,onChange:e=>x(e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(l._,{htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx(d.I,{id:"confirmPassword",type:"password",value:h,onChange:e=>g(e.target.value),required:!0}),v&&s.jsx("p",{className:"text-sm text-destructive",children:v})]})]}),(0,s.jsxs)(c.eW,{className:"flex flex-col space-y-4",children:[s.jsx(n.z,{type:"submit",className:"w-full",disabled:w,children:w?(0,s.jsxs)(s.Fragment,{children:[s.jsx(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating account..."]}):"Register"}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:["Already have an account?"," ",s.jsx(i.default,{href:"/login",className:"font-medium text-primary underline-offset-4 hover:underline",children:"Login"})]})]})]})]})]})}},8053:(e,r,t)=>{"use strict";t.d(r,{d:()=>d,z:()=>l});var s=t(7247),a=t(8964),i=t(9008),o=t(7972),n=t(5008);let d=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-[0.98]",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:border-accent",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md",ghost:"hover:bg-accent/50 hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md hover:shadow-lg hover:from-blue-700 hover:to-indigo-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},l)=>{let c=a?i.g7:"button";return s.jsx(c,{className:(0,n.cn)(d({variant:r,size:t,className:e})),ref:l,...o})});l.displayName="Button"},7757:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>n,SZ:()=>l,Zb:()=>o,aY:()=>c,eW:()=>u,ll:()=>d});var s=t(7247),a=t(8964),i=t(5008);let o=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md",e),...r}));o.displayName="Card";let n=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));n.displayName="CardHeader";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));u.displayName="CardFooter"},6991:(e,r,t)=>{"use strict";t.d(r,{I:()=>o});var s=t(7247),a=t(8964),i=t(5008);let o=a.forwardRef(({className:e,type:r,...t},a)=>s.jsx("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));o.displayName="Input"},2394:(e,r,t)=>{"use strict";t.d(r,{_:()=>l});var s=t(7247),a=t(8964),i=t(768),o=t(7972),n=t(5008);let d=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...r},t)=>s.jsx(i.f,{ref:t,className:(0,n.cn)(d(),e),...r}));l.displayName=i.f.displayName},2310:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(6323).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},8749:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});let s=(0,t(6323).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2238:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,dynamic:()=>a});var s=t(5347);let a=(0,s.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(auth)\register\page.tsx#dynamic`),i=(0,s.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(auth)\register\page.tsx#default`)},768:(e,r,t)=>{"use strict";t.d(r,{f:()=>n});var s=t(8964),a=t(2251),i=t(7247),o=s.forwardRef((e,r)=>(0,i.jsx)(a.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var n=o}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[475,43],()=>t(3779));module.exports=s})();