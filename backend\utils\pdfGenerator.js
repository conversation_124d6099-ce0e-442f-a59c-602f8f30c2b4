const puppeteer = require("puppeteer");
const path = require("path");
const fs = require("fs");

/**
 * Generate PDF from HTML content
 * @param {string} htmlContent - HTML content to convert to PDF
 * @param {string} documentType - Type of document (resume, coverLetter, linkedin)
 * @param {string} title - Document title
 * @returns {Promise<Buffer>} - PDF buffer
 */
exports.generatePdf = async (htmlContent, documentType, title) => {
  try {
    // Launch a headless browser
    const browser = await puppeteer.launch({
      headless: "new",
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const page = await browser.newPage();

    // Set content to the page
    await page.setContent(htmlContent, {
      waitUntil: "networkidle0",
    });

    // Set PDF options
    const pdfOptions = {
      format: "A4",
      printBackground: true,
      margin: {
        top: "20px",
        right: "20px",
        bottom: "20px",
        left: "20px",
      },
      displayHeaderFooter: true,
      headerTemplate: "<div></div>",
      footerTemplate: `
        <div style="width: 100%; font-size: 8px; text-align: center; color: #777;">
          <span>Generated by CareerPilotAI - Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
        </div>
      `,
    };

    // Generate PDF
    const pdfBuffer = await page.pdf(pdfOptions);

    // Close the browser
    await browser.close();

    return pdfBuffer;
  } catch (error) {
    console.error("PDF Generation Error:", error);
    throw new Error("Failed to generate PDF");
  }
};

/**
 * Convert plain text to HTML with proper formatting
 * @param {string} text - Plain text content
 * @param {string} documentType - Type of document (resume, coverLetter, linkedin)
 * @param {string} title - Document title
 * @param {boolean} isHtml - Whether the text is already HTML
 * @returns {string} - Formatted HTML
 */
exports.formatToHtml = (text, documentType, title, isHtml = false) => {
  // Get the appropriate template based on document type
  const templatePath = path.join(
    __dirname,
    `../pdf/templates/${documentType}Template.html`
  );

  try {
    // Read the template file
    let template = fs.existsSync(templatePath)
      ? fs.readFileSync(templatePath, "utf8")
      : getDefaultTemplate(documentType);

    // Format the content if it's not already HTML
    const formattedContent = isHtml
      ? text
      : formatTextContent(text, documentType);

    // Replace placeholders in the template
    template = template
      .replace(/{{title}}/g, title || "Resume") // Replace all instances of {{title}} and provide a default
      .replace("{{content}}", formattedContent)
      .replace("{{date}}", new Date().toLocaleDateString());

    return template;
  } catch (error) {
    console.error("HTML Formatting Error:", error);
    // Fallback to a simple HTML structure
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }
          h1 { color: #333; }
          .content { white-space: pre-wrap; }
          .section { margin-bottom: 20px; }
          h2, h3, h4 { color: #2c3e50; margin-top: 20px; }
          .subtitle { color: #7f8c8d; font-size: 1.1em; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <h1 id="document-title">${title || "Resume"}</h1>
        <div class="content">${
          isHtml ? text : text.replace(/\n/g, "<br>")
        }</div>
      </body>
      </html>
    `;
  }
};

/**
 * Format text content based on document type
 * @param {string} text - Plain text content
 * @param {string} documentType - Type of document
 * @returns {string} - Formatted HTML content
 */
function formatTextContent(text, documentType) {
  // Convert line breaks to <br> tags
  let formatted = text.replace(/\n/g, "<br>");

  // Handle different document types
  if (documentType === "resume") {
    // Enhance headings
    formatted = formatted.replace(/^([A-Z][A-Z\s]+):/gm, "<h2>$1</h2>");

    // Add section divs around content between headings
    formatted = formatted.replace(
      /<h2>(.*?)<\/h2>(.*?)(?=<h2>|$)/gs,
      '<h2>$1</h2><div class="section">$2</div>'
    );

    // Format bullet points for skills and other lists
    formatted = formatted.replace(/•\s(.*?)(?:<br>|$)/g, "<li>$1</li>");
    formatted = formatted.replace(/<li>(.*?)<\/li>/g, "<ul><li>$1</li></ul>");
    formatted = formatted.replace(/<\/ul><ul>/g, "");

    // Format skills section specifically
    formatted = formatted.replace(
      /<h2>SKILLS<\/h2><div class="section">(.*?)<\/div>/gs,
      (match, skills) => {
        // Check if skills are already in a list format
        if (skills.includes("<ul>")) {
          return `<h2>SKILLS</h2><div class="section">${skills}</div>`;
        }

        // Convert comma-separated skills to bullet points if not already in list format
        const skillsList = skills.split(/,\s*/).filter((skill) => skill.trim());
        if (skillsList.length > 1) {
          const skillsHtml = skillsList
            .map((skill) => `<li>${skill.trim()}</li>`)
            .join("");
          return `<h2>SKILLS</h2><div class="section"><ul>${skillsHtml}</ul></div>`;
        }

        return match;
      }
    );
  } else if (documentType === "coverLetter") {
    // Format paragraphs with proper spacing
    formatted = formatted.replace(/<br><br>/g, "</p><p>");
    formatted = `<p>${formatted}</p>`;
    formatted = formatted.replace(/<p><br>/g, "<p>");
    formatted = formatted.replace(/<br><\/p>/g, "</p>");

    // Format greeting and signature
    formatted = formatted.replace(
      /(Dear .*?),(<br>|<\/p>)/i,
      '<div class="greeting">$1,</div>$2'
    );
    formatted = formatted.replace(
      /(Sincerely|Regards|Respectfully|Thank you|Best regards|Yours truly),(<br>|<\/p>)/i,
      '<div class="closing">$1,</div>$2'
    );
  } else if (documentType === "linkedin") {
    // Format sections with proper spacing
    formatted = formatted.replace(/^([A-Z][A-Za-z\s]+):/gm, "<h3>$1</h3>");

    // Add section divs around content between headings
    formatted = formatted.replace(
      /<h3>(.*?)<\/h3>(.*?)(?=<h3>|$)/gs,
      '<h3>$1</h3><div class="section">$2</div>'
    );

    // Format skills as a list if comma-separated
    formatted = formatted.replace(
      /<h3>Skills<\/h3><div class="section">(.*?)<\/div>/gi,
      (match, skills) => {
        // Check if skills are already in a list format
        if (skills.includes("<ul>")) {
          return match;
        }

        // Convert comma-separated skills to a formatted list
        const skillsList = skills.split(/,\s*/).filter((skill) => skill.trim());
        if (skillsList.length > 1) {
          const skillsHtml = skillsList
            .map((skill) => `<span class="skill-item">${skill.trim()}</span>`)
            .join("");
          return `<h3>Skills</h3><div class="section"><div class="skills-list">${skillsHtml}</div></div>`;
        }

        return match;
      }
    );
  }

  return formatted;
}

/**
 * Get default template for a document type
 * @param {string} documentType - Type of document
 * @returns {string} - Default HTML template
 */
function getDefaultTemplate(documentType) {
  const commonStyles = `
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    h2 {
      color: #3498db;
      margin-top: 20px;
    }
    ul {
      margin-top: 5px;
    }
    .date {
      color: #7f8c8d;
      font-size: 0.9em;
      text-align: right;
    }
    .content {
      margin-top: 20px;
    }
  `;

  if (documentType === "resume") {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>{{title}}</title>
        <style>
          ${commonStyles}
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1 id="document-title">{{title}}</h1>
        </div>
        <div class="date">Generated on {{date}}</div>
        <div class="content">{{content}}</div>
        <div class="footer">
          Generated by CareerPilotAI | Professional Career Documents
        </div>
      </body>
      </html>
    `;
  } else if (documentType === "coverLetter") {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>{{title}}</title>
        <style>
          ${commonStyles}
          .letter-header {
            text-align: right;
            margin-bottom: 30px;
          }
        </style>
      </head>
      <body>
        <div class="letter-header">
          <div>{{date}}</div>
        </div>
        <h1 id="document-title">{{title}}</h1>
        <div class="content">{{content}}</div>
        <div class="footer">
          Generated by CareerPilotAI | Professional Career Documents
        </div>
      </body>
      </html>
    `;
  } else if (documentType === "linkedin") {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>{{title}}</title>
        <style>
          ${commonStyles}
          h1 {
            margin-bottom: 5px;
          }
          h2 {
            color: #0077b5;
            margin-top: 5px;
            margin-bottom: 10px;
          }
          h3 {
            color: #0077b5;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-top: 25px;
          }
          h4 {
            margin-bottom: 5px;
          }
          .subtitle {
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
          }
          .section {
            margin-bottom: 20px;
          }
        </style>
      </head>
      <body>
        <div class="date">Generated on {{date}}</div>
        <div class="content">{{content}}</div>
        <div class="footer">
          Generated by CareerPilotAI | Professional Career Documents
        </div>
      </body>
      </html>
    `;
  } else {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>{{title}}</title>
        <style>
          ${commonStyles}
        </style>
      </head>
      <body>
        <h1 id="document-title">{{title}}</h1>
        <div class="date">Generated on {{date}}</div>
        <div class="content">{{content}}</div>
        <div class="footer">
          Generated by CareerPilotAI | Professional Career Documents
        </div>
      </body>
      </html>
    `;
  }
}
