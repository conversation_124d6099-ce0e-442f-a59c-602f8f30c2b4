(()=>{var e={};e.id=688,e.ids=[688],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},4497:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o}),a(9795),a(2626),a(546);var r=a(170),t=a(5002),n=a(3876),i=a.n(n),l=a(6299),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let o=["",{children:["(dashboard)",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9795)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\profile\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\profile\\page.tsx"],m="/(dashboard)/profile/page",u={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/(dashboard)/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},1620:(e,s,a)=>{Promise.resolve().then(a.bind(a,1637))},1637:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D,dynamic:()=>M});var r=a(7247),t=a(8964),n=a(9906),i=a(9767),l=a(5503),d=a(7757),o=a(8053),c=a(6991),m=a(2394),u=a(7641),x=a(1897),p=a(4662),h=a(1600),f=a(1365),g=a(4445),v=a(906),j=a(7734),y=a(8339),b=a(6323);let N=(0,b.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var w=a(5271);let P=(0,b.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var k=a(6903),C=a(9379),G=a(8918),S=a(8749),I=a(9400);let Z=(0,b.Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),L=(0,b.Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),q=(0,b.Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var A=a(6285);let M="force-dynamic";function D(){let{user:e,logout:s}=(0,j.a)(),[a,b]=(0,t.useState)(null),[M,D]=(0,t.useState)(null),[U,z]=(0,t.useState)(!0),[F,T]=(0,t.useState)(!1),[R,V]=(0,t.useState)(!1),[_,$]=(0,t.useState)(!1),[E,Y]=(0,t.useState)("profile"),[O,W]=(0,t.useState)(""),[K,Q]=(0,t.useState)(""),[H,B]=(0,t.useState)(""),[X,J]=(0,t.useState)(""),[ee,es]=(0,t.useState)(""),[ea,er]=(0,t.useState)(""),{toast:et}=(0,v.pm)(),en=e=>e&&""!==e.trim()?e.split(" ").filter(e=>e.length>0).map(e=>e[0]).join("").toUpperCase().substring(0,2):"U",ei=(e,s)=>e&&s?s<=0?100:Math.min(Math.round(e/s*100),100):0,el=e=>e>=80?"bg-red-500":e>=50?"bg-amber-500":"bg-green-500",ed=(e,s)=>e?e.usage&&e.usage[s]?e.usage[s]:e[s]?e[s]:0:0,eo=(e,s)=>e?e.limits&&e.limits[s]?e.limits[s]:e[`${s}Limit`]?e[`${s}Limit`]:1:1,ec=async()=>{z(!0);try{try{let[e,s]=await Promise.all([g.TV.getProfile(),g.TV.getUsageStats()]);console.log("Profile API response:",e),console.log("Usage API response:",s);let a=e.data.data?.user||e.data.user||e.data;console.log("Extracted user data:",a),b(a);let r=s.data.data||s.data;console.log("Extracted usage data:",r),D(r),W(a?.name||""),Q(a?.email||"")}catch(s){console.error("API error:",s);let e={id:"mock-user-id",name:"Demo User",email:"<EMAIL>",planType:"free",createdAt:new Date().toISOString()};b(e),D({usage:{resumeGenerations:0,coverLetterGenerations:0,linkedinGenerations:0},limits:{resumeGenerations:1,coverLetterGenerations:1,linkedinGenerations:1},planType:"free"}),W(e.name),Q(e.email),et({title:"Demo Mode",description:"Using demo data since the API is not available."})}}catch(e){et({title:"Error fetching profile",description:"Could not load your profile information. Please try again.",variant:"destructive"})}finally{z(!1)}},em=async()=>{T(!0);try{await ec(),et({title:"Profile refreshed",description:"Your profile information has been updated."})}catch(e){}finally{T(!1)}},eu=async e=>{if(e.preventDefault(),!H){et({title:"Password required",description:"Please enter your current password to confirm these changes.",variant:"destructive"});return}V(!0);try{let e=await g.TV.updateProfile({name:O,email:K,currentPassword:H}),s=e.data.user||e.data;b(s),B(""),et({title:"Profile updated",description:"Your profile has been updated successfully"})}catch(s){let e="Could not update your profile. Please try again.";s.response?.status===401?e="Incorrect password. Please try again.":s.response?.data?.error&&(e=s.response.data.error),et({title:"Update failed",description:e,variant:"destructive"})}finally{V(!1)}},ex=async e=>{if(e.preventDefault(),X!==ee){er("New passwords do not match");return}if(X.length<6){er("Password must be at least 6 characters");return}er(""),V(!0);try{await g.TV.updatePassword({currentPassword:H,newPassword:X,confirmPassword:ee}),et({title:"Password updated",description:"Your password has been updated successfully",variant:"success",duration:5e3,key:"password-update-success"}),B(""),J(""),es("")}catch(a){console.error("Password update error:",a);let e="Could not update your password. Please try again.",s=0;if(a.response&&(s=a.response.status,a.response.data&&a.response.data.message&&(e=a.response.data.message)),et({title:"Update failed",description:e,variant:"destructive",duration:5e3,key:`password-update-error-${s}`}),401===s){er("Current password is incorrect");let e=document.getElementById("currentPassword");e&&e.focus()}}finally{V(!1)}},ep=async()=>{$(!0);try{await s()}catch(e){et({title:"Logout failed",description:"Could not log you out. Please try again.",variant:"destructive"}),$(!1)}};return(0,r.jsxs)(i.r,{children:[r.jsx(l.x,{heading:"Profile",text:"Manage your account settings and preferences",children:(0,r.jsxs)(o.z,{variant:"outline",size:"icon",onClick:em,disabled:F,className:"h-9 w-9 transition-all duration-300 hover:bg-primary/10 hover:border-primary/30",children:[r.jsx(y.Z,{className:`h-4 w-4 ${F?"animate-spin":"transition-transform duration-300 hover:rotate-180"}`}),r.jsx("span",{className:"sr-only",children:"Refresh"})]})}),U?(0,r.jsxs)("div",{className:"grid gap-8 md:grid-cols-3",children:[r.jsx(x.O,{className:"h-[300px] w-full"}),r.jsx(x.O,{className:"h-[300px] w-full md:col-span-2"})]}):(0,r.jsxs)("div",{className:"grid gap-8 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)(d.Zb,{className:"overflow-hidden border-muted-foreground/20 transition-all duration-300 hover:shadow-md",children:[r.jsx(d.Ol,{className:"bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950/50 dark:to-slate-900/30 pb-6",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsxs)(h.qE,{className:`h-24 w-24 ${(()=>{let s=["bg-blue-500","bg-green-500","bg-purple-500","bg-pink-500","bg-indigo-500","bg-teal-500","bg-orange-500","bg-red-500","bg-amber-500","bg-emerald-500","bg-cyan-500","bg-violet-500"],r=a?.name||e?.name;return r?s[r.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]:s[0]})()} text-white mb-4`,children:[r.jsx(h.F$,{src:"",alt:a?.name||e?.name||"User"}),r.jsx(h.Q5,{className:"text-2xl font-semibold",children:a?.name?en(a.name):e?.name?en(e.name):"U"})]}),r.jsx(d.ll,{className:"text-center",children:"Your Profile"}),(0,r.jsxs)(f.C,{variant:"outline",className:"mt-2 capitalize",children:[a?.planType||e?.planType||"Free"," Plan"]})]})}),r.jsx(d.aY,{className:"pt-4 pb-2",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-muted-foreground",children:[r.jsx(N,{className:"h-4 w-4"}),"Member Since"]}),r.jsx("span",{className:"font-medium",children:a?.createdAt?new Date(a.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A"})]}),r.jsx(u.Z,{}),M&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("h3",{className:"text-sm font-medium flex items-center justify-between",children:[r.jsx("span",{children:"Account Usage"}),r.jsx(f.C,{variant:"outline",className:"capitalize",children:M.planType||a?.planType||"Free"})]}),(0,r.jsxs)("div",{className:"bg-muted/20 p-3 rounded-md border border-muted/30 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-muted-foreground",children:[r.jsx(w.Z,{className:"h-4 w-4"}),"Name"]}),r.jsx("span",{className:"font-medium truncate max-w-[150px]",title:a?.name||e?.name||"User",children:a?.name||e?.name||"User"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-muted-foreground",children:[r.jsx(P,{className:"h-4 w-4"}),"Email"]}),r.jsx("span",{className:"font-medium truncate max-w-[150px]",title:a?.email||e?.email||"<EMAIL>",children:a?.email||e?.email||"<EMAIL>"})]})]}),(0,r.jsxs)("div",{className:"bg-muted/20 p-3 rounded-md border border-muted/30",children:[r.jsx("h4",{className:"text-xs font-semibold uppercase mb-3",children:"Remaining Limits"}),(0,r.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-muted-foreground",children:[r.jsx(k.Z,{className:"h-4 w-4"}),"Resumes"]}),(0,r.jsxs)("span",{className:"font-medium",children:[ed(M,"resumeGenerations")," /",0>eo(M,"resumeGenerations")?"∞":eo(M,"resumeGenerations")]})]}),r.jsx("div",{className:"relative pt-1",children:r.jsx("div",{className:"overflow-hidden h-2 text-xs flex rounded bg-gray-200 dark:bg-gray-700",children:r.jsx("div",{className:`${el(ei(ed(M,"resumeGenerations"),eo(M,"resumeGenerations")))} h-full rounded`,style:{width:`${ei(ed(M,"resumeGenerations"),eo(M,"resumeGenerations"))}%`}})})}),r.jsx("div",{className:"text-xs text-right font-medium",children:(0,r.jsxs)("span",{className:eo(M,"resumeGenerations")-ed(M,"resumeGenerations")<=0?"text-red-500":"text-green-500",children:[0>eo(M,"resumeGenerations")?"Unlimited":Math.max(0,eo(M,"resumeGenerations")-ed(M,"resumeGenerations")),0>eo(M,"resumeGenerations")?"":" remaining"]})})]}),(0,r.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-muted-foreground",children:[r.jsx(C.Z,{className:"h-4 w-4"}),"Cover Letters"]}),(0,r.jsxs)("span",{className:"font-medium",children:[ed(M,"coverLetterGenerations")," /",0>eo(M,"coverLetterGenerations")?"∞":eo(M,"coverLetterGenerations")]})]}),r.jsx("div",{className:"relative pt-1",children:r.jsx("div",{className:"overflow-hidden h-2 text-xs flex rounded bg-gray-200 dark:bg-gray-700",children:r.jsx("div",{className:`${el(ei(ed(M,"coverLetterGenerations"),eo(M,"coverLetterGenerations")))} h-full rounded`,style:{width:`${ei(ed(M,"coverLetterGenerations"),eo(M,"coverLetterGenerations"))}%`}})})}),r.jsx("div",{className:"text-xs text-right font-medium",children:(0,r.jsxs)("span",{className:eo(M,"coverLetterGenerations")-ed(M,"coverLetterGenerations")<=0?"text-red-500":"text-green-500",children:[0>eo(M,"coverLetterGenerations")?"Unlimited":Math.max(0,eo(M,"coverLetterGenerations")-ed(M,"coverLetterGenerations")),0>eo(M,"coverLetterGenerations")?"":" remaining"]})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-muted-foreground",children:[r.jsx(C.Z,{className:"h-4 w-4"}),"LinkedIn Profiles"]}),(0,r.jsxs)("span",{className:"font-medium",children:[ed(M,"linkedinGenerations")," /",0>eo(M,"linkedinGenerations")?"∞":eo(M,"linkedinGenerations")]})]}),r.jsx("div",{className:"relative pt-1",children:r.jsx("div",{className:"overflow-hidden h-2 text-xs flex rounded bg-gray-200 dark:bg-gray-700",children:r.jsx("div",{className:`${el(ei(ed(M,"linkedinGenerations"),eo(M,"linkedinGenerations")))} h-full rounded`,style:{width:`${ei(ed(M,"linkedinGenerations"),eo(M,"linkedinGenerations"))}%`}})})}),r.jsx("div",{className:"text-xs text-right font-medium",children:(0,r.jsxs)("span",{className:eo(M,"linkedinGenerations")-ed(M,"linkedinGenerations")<=0?"text-red-500":"text-green-500",children:[0>eo(M,"linkedinGenerations")?"Unlimited":Math.max(0,eo(M,"linkedinGenerations")-ed(M,"linkedinGenerations")),0>eo(M,"linkedinGenerations")?"":" remaining"]})})]})]}),("free"===M.planType||a?.planType==="free")&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground bg-muted/30 p-2 rounded border border-muted",children:[r.jsx("p",{className:"font-medium mb-1",children:"Free Plan Limitations"}),r.jsx("p",{children:"Upgrade to unlock more document generations and premium features."})]})]})]})}),(0,r.jsxs)(d.eW,{className:"border-t p-4 bg-muted/5 flex flex-col gap-2",children:[r.jsx(o.z,{variant:"outline",size:"sm",className:"w-full gap-1 hover:bg-primary hover:text-primary-foreground transition-all duration-300",asChild:!0,children:(0,r.jsxs)(n.default,{href:"/subscription",children:[r.jsx(G.Z,{className:"mr-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"}),"Upgrade Plan"]})}),r.jsx(o.z,{variant:"ghost",size:"sm",className:"w-full gap-1 text-red-600 hover:bg-red-100 hover:text-red-700 dark:hover:bg-red-950 dark:hover:text-red-500 transition-all duration-300",onClick:ep,disabled:_,children:_?(0,r.jsxs)(r.Fragment,{children:[r.jsx(S.Z,{className:"mr-1 h-4 w-4 animate-spin"}),"Logging out..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(I.Z,{className:"mr-1 h-4 w-4"}),"Log Out"]})})]})]}),(0,r.jsxs)(d.Zb,{className:"overflow-hidden border-muted-foreground/20 transition-all duration-300 hover:shadow-md",children:[(0,r.jsxs)(d.Ol,{children:[(0,r.jsxs)(d.ll,{className:"flex items-center gap-2",children:[r.jsx(Z,{className:"h-5 w-5 text-muted-foreground"}),"Account Security"]}),r.jsx(d.SZ,{children:"Security status and settings"})]}),(0,r.jsxs)(d.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-sm",children:[r.jsx(L,{className:"h-4 w-4 text-green-500"}),"Password"]}),r.jsx(f.C,{variant:"outline",children:"Set"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"flex items-center gap-2 text-sm",children:[r.jsx(q,{className:"h-4 w-4 text-amber-500"}),"Two-Factor Auth"]}),r.jsx(f.C,{variant:"outline",className:"bg-muted",children:"Not Set"})]})]})]})]}),r.jsx("div",{className:"md:col-span-2",children:(0,r.jsxs)(p.mQ,{value:E,onValueChange:Y,className:"w-full",children:[(0,r.jsxs)(p.dr,{className:"grid w-full grid-cols-2",children:[(0,r.jsxs)(p.SP,{value:"profile",className:"flex items-center gap-1",children:[r.jsx(w.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"hidden sm:inline",children:"Profile"})]}),(0,r.jsxs)(p.SP,{value:"security",className:"flex items-center gap-1",children:[r.jsx(A.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"hidden sm:inline",children:"Security"})]})]}),r.jsx(p.nU,{value:"profile",className:"mt-6 space-y-4",children:(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{children:[r.jsx(d.ll,{children:"Personal Information"}),r.jsx(d.SZ,{children:"Update your personal details"})]}),(0,r.jsxs)("form",{onSubmit:eu,children:[(0,r.jsxs)(d.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m._,{htmlFor:"name",children:"Full Name"}),r.jsx(c.I,{id:"name",value:O,onChange:e=>W(e.target.value),placeholder:"Your full name",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m._,{htmlFor:"email",children:"Email Address"}),r.jsx(c.I,{id:"email",type:"email",value:K,onChange:e=>Q(e.target.value),placeholder:"Your email address",required:!0}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"This email will be used for account notifications and communications."})]}),(0,r.jsxs)("div",{className:"space-y-2 pt-4 border-t",children:[r.jsx(m._,{htmlFor:"confirmPassword",className:"font-medium text-red-500",children:"Confirm with Password"}),r.jsx(c.I,{id:"confirmPassword",type:"password",value:H,onChange:e=>B(e.target.value),placeholder:"Enter your current password to save changes",required:!0}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"For security reasons, please enter your current password to confirm these changes."})]})]}),(0,r.jsxs)(d.eW,{className:"flex justify-between border-t pt-6",children:[r.jsx(o.z,{type:"button",variant:"outline",children:"Cancel"}),r.jsx(o.z,{type:"submit",disabled:R,children:R?(0,r.jsxs)(r.Fragment,{children:[r.jsx(S.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Save Changes"})]})]})]})}),(0,r.jsxs)(p.nU,{value:"security",className:"mt-6 space-y-4",children:[(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{children:[r.jsx(d.ll,{children:"Change Password"}),r.jsx(d.SZ,{children:"Update your account password"})]}),(0,r.jsxs)("form",{onSubmit:ex,children:[(0,r.jsxs)(d.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m._,{htmlFor:"currentPassword",children:"Current Password"}),r.jsx(c.I,{id:"currentPassword",type:"password",value:H,onChange:e=>B(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m._,{htmlFor:"newPassword",children:"New Password"}),r.jsx(c.I,{id:"newPassword",type:"password",value:X,onChange:e=>J(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(m._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),r.jsx(c.I,{id:"confirmPassword",type:"password",value:ee,onChange:e=>es(e.target.value),required:!0}),ea&&r.jsx("p",{className:"text-sm text-red-500 mt-1",children:ea})]}),r.jsx("div",{className:"text-sm text-muted-foreground",children:r.jsx("p",{children:"Password must be at least 6 characters long."})})]}),(0,r.jsxs)(d.eW,{className:"flex justify-between border-t pt-6",children:[r.jsx(o.z,{type:"button",variant:"outline",children:"Cancel"}),r.jsx(o.z,{type:"submit",disabled:R,children:R?(0,r.jsxs)(r.Fragment,{children:[r.jsx(S.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update Password"})]})]})]}),(0,r.jsxs)(d.Zb,{children:[(0,r.jsxs)(d.Ol,{children:[r.jsx(d.ll,{children:"Two-Factor Authentication"}),r.jsx(d.SZ,{children:"Add an extra layer of security to your account"})]}),r.jsx(d.aY,{children:r.jsx("div",{className:"flex flex-col space-y-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[r.jsx(Z,{className:"h-8 w-8 text-muted-foreground mt-1"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium",children:"Protect your account with 2FA"}),r.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in."})]})]})})}),r.jsx(d.eW,{children:r.jsx(o.z,{variant:"outline",className:"w-full",children:"Enable Two-Factor Authentication"})})]})]})]})})]})]})}},7757:(e,s,a)=>{"use strict";a.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>i,aY:()=>c,eW:()=>m,ll:()=>d});var r=a(7247),t=a(8964),n=a(5008);let i=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md",e),...s}));i.displayName="Card";let l=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=t.forwardRef(({className:e,...s},a)=>r.jsx("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},6991:(e,s,a)=>{"use strict";a.d(s,{I:()=>i});var r=a(7247),t=a(8964),n=a(5008);let i=t.forwardRef(({className:e,type:s,...a},t)=>r.jsx("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...a}));i.displayName="Input"},2394:(e,s,a)=>{"use strict";a.d(s,{_:()=>o});var r=a(7247),t=a(8964),n=a(768),i=a(7972),l=a(5008);let d=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=t.forwardRef(({className:e,...s},a)=>r.jsx(n.f,{ref:a,className:(0,l.cn)(d(),e),...s}));o.displayName=n.f.displayName},7641:(e,s,a)=>{"use strict";a.d(s,{Z:()=>c});var r=a(7247),t=a(8964),n=a(2251),i="horizontal",l=["horizontal","vertical"],d=t.forwardRef((e,s)=>{let{decorative:a,orientation:t=i,...d}=e,o=l.includes(t)?t:i;return(0,r.jsx)(n.WV.div,{"data-orientation":o,...a?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...d,ref:s})});d.displayName="Separator";var o=a(5008);let c=t.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...t},n)=>r.jsx(d,{ref:n,decorative:a,orientation:s,className:(0,o.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...t}));c.displayName=d.displayName},1897:(e,s,a)=>{"use strict";a.d(s,{O:()=>n});var r=a(7247),t=a(5008);function n({className:e,...s}){return r.jsx("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",e),...s})}},4662:(e,s,a)=>{"use strict";a.d(s,{SP:()=>o,dr:()=>d,mQ:()=>l,nU:()=>c});var r=a(7247),t=a(8964),n=a(8754),i=a(5008);let l=n.fC,d=t.forwardRef(({className:e,...s},a)=>r.jsx(n.aV,{ref:a,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));d.displayName=n.aV.displayName;let o=t.forwardRef(({className:e,...s},a)=>r.jsx(n.xz,{ref:a,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));o.displayName=n.xz.displayName;let c=t.forwardRef(({className:e,...s},a)=>r.jsx(n.VY,{ref:a,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=n.VY.displayName},6285:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(6323).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},8339:(e,s,a)=>{"use strict";a.d(s,{Z:()=>r});let r=(0,a(6323).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9795:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n,dynamic:()=>t});var r=a(5347);let t=(0,r.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\profile\page.tsx#dynamic`),n=(0,r.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\profile\page.tsx#default`)},768:(e,s,a)=>{"use strict";a.d(s,{f:()=>l});var r=a(8964),t=a(2251),n=a(7247),i=r.forwardRef((e,s)=>(0,n.jsx)(t.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var l=i},8754:(e,s,a)=>{"use strict";a.d(s,{VY:()=>L,aV:()=>I,fC:()=>S,xz:()=>Z});var r=a(8964),t=a(319),n=a(732),i=a(5706),l=a(7264),d=a(2251),o=a(1310),c=a(8469),m=a(7015),u=a(7247),x="Tabs",[p,h]=(0,n.b)(x,[i.Pc]),f=(0,i.Pc)(),[g,v]=p(x),j=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:t,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:x="automatic",...p}=e,h=(0,o.gm)(l),[f,v]=(0,c.T)({prop:r,onChange:t,defaultProp:n});return(0,u.jsx)(g,{scope:a,baseId:(0,m.M)(),value:f,onValueChange:v,orientation:i,dir:h,activationMode:x,children:(0,u.jsx)(d.WV.div,{dir:h,"data-orientation":i,...p,ref:s})})});j.displayName=x;var y="TabsList",b=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...t}=e,n=v(y,a),l=f(a);return(0,u.jsx)(i.fC,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:r,children:(0,u.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...t,ref:s})})});b.displayName=y;var N="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...l}=e,o=v(N,a),c=f(a),m=C(o.baseId,r),x=G(o.baseId,r),p=r===o.value;return(0,u.jsx)(i.ck,{asChild:!0,...c,focusable:!n,active:p,children:(0,u.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...l,ref:s,onMouseDown:(0,t.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,t.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,t.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;p||n||!e||o.onValueChange(r)})})})});w.displayName=N;var P="TabsContent",k=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,forceMount:n,children:i,...o}=e,c=v(P,a),m=C(c.baseId,t),x=G(c.baseId,t),p=t===c.value,h=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(l.z,{present:n||p,children:({present:a})=>(0,u.jsx)(d.WV.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:x,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&i})})});function C(e,s){return`${e}-trigger-${s}`}function G(e,s){return`${e}-content-${s}`}k.displayName=P;var S=j,I=b,Z=w,L=k}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[475,97,12,43,967],()=>a(4497));module.exports=r})();