(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},5606:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(1687),t(2626),t(546);var s=t(170),a=t(5002),l=t(3876),o=t.n(l),n=t(6299),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(r,i);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1687)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\page.tsx"],m="/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8496:(e,r,t)=>{Promise.resolve().then(t.bind(t,2338)),Promise.resolve().then(t.t.bind(t,4080,23))},2338:(e,r,t)=>{"use strict";t.d(r,{LandingHeader:()=>u});var s=t(7247),a=t(9906),l=t(7734),o=t(8053),n=t(8351),i=t(2310),d=t(6323);let c=(0,d.Z)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),m=(0,d.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var x=t(6683),p=t(3326);function u(){let{isAuthenticated:e}=(0,l.a)();return s.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200",children:(0,s.jsxs)("div",{className:"container flex h-16 items-center justify-between px-4 md:px-6",children:[(0,s.jsxs)(a.default,{href:"/",className:"flex items-center space-x-2 py-2 transition-all duration-200 hover:opacity-80",children:[s.jsx(i.Z,{className:"h-5 w-5 text-primary animate-fade-in"}),s.jsx("span",{className:"font-bold text-base bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70 animate-fade-in",children:"CareerPilotAI"})]}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8 text-sm font-medium",children:[s.jsx(a.default,{href:"/#features",className:"relative px-1 py-2 transition-colors hover:text-foreground text-foreground/70 after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-0 after:bg-primary after:transition-all hover:after:w-full",children:"Features"}),s.jsx(a.default,{href:"/#pricing",className:"relative px-1 py-2 transition-colors hover:text-foreground text-foreground/70 after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-0 after:bg-primary after:transition-all hover:after:w-full",children:"Pricing"}),s.jsx(a.default,{href:"/about",className:"relative px-1 py-2 transition-colors hover:text-foreground text-foreground/70 after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-0 after:bg-primary after:transition-all hover:after:w-full",children:"About"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 py-2",children:[s.jsx(n.l,{}),s.jsx("div",{className:"hidden md:flex items-center space-x-3",children:e?s.jsx(o.z,{asChild:!0,variant:"gradient",size:"sm",className:"animate-fade-in",children:s.jsx(a.default,{href:"/dashboard",children:"Dashboard"})}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(o.z,{variant:"ghost",asChild:!0,size:"sm",className:"group",children:(0,s.jsxs)(a.default,{href:"/login",className:"flex items-center",children:[s.jsx(c,{className:"mr-1 h-4 w-4 transition-transform group-hover:-translate-y-0.5 group-hover:translate-x-0.5"}),s.jsx("span",{children:"Login"})]})}),s.jsx(o.z,{asChild:!0,variant:"gradient",size:"sm",className:"animate-fade-in",children:(0,s.jsxs)(a.default,{href:"/register",className:"flex items-center",children:[s.jsx(m,{className:"mr-1 h-4 w-4"}),s.jsx("span",{children:"Register"})]})})]})}),(0,s.jsxs)(p.yo,{children:[s.jsx(p.aM,{asChild:!0,children:(0,s.jsxs)(o.z,{variant:"ghost",size:"icon",className:"md:hidden rounded-full",children:[s.jsx(x.Z,{className:"h-5 w-5"}),s.jsx("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,s.jsxs)(p.ue,{side:"right",className:"border-l-primary/10 w-[250px]",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[s.jsx(i.Z,{className:"h-5 w-5 text-primary"}),s.jsx("span",{className:"font-bold text-base bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70",children:"CareerPilotAI"})]}),(0,s.jsxs)("nav",{className:"flex flex-col space-y-4 text-sm font-medium",children:[s.jsx(a.default,{href:"/#features",className:"flex items-center px-2 py-2 rounded-md transition-colors hover:bg-accent hover:text-foreground",children:"Features"}),s.jsx(a.default,{href:"/#pricing",className:"flex items-center px-2 py-2 rounded-md transition-colors hover:bg-accent hover:text-foreground",children:"Pricing"}),s.jsx(a.default,{href:"/about",className:"flex items-center px-2 py-2 rounded-md transition-colors hover:bg-accent hover:text-foreground",children:"About"}),s.jsx("div",{className:"h-px w-full bg-border my-2"}),e?s.jsx(o.z,{asChild:!0,variant:"gradient",size:"sm",className:"w-full",children:s.jsx(a.default,{href:"/dashboard",children:"Dashboard"})}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(o.z,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,s.jsxs)(a.default,{href:"/login",className:"flex items-center justify-center",children:[s.jsx(c,{className:"mr-1 h-4 w-4"}),s.jsx("span",{children:"Login"})]})}),s.jsx(o.z,{asChild:!0,variant:"gradient",size:"sm",className:"w-full",children:(0,s.jsxs)(a.default,{href:"/register",className:"flex items-center justify-center",children:[s.jsx(m,{className:"mr-1 h-4 w-4"}),s.jsx("span",{children:"Register"})]})})]})]})]})]})]})]})})}},8351:(e,r,t)=>{"use strict";t.d(r,{l:()=>d});var s=t(7247),a=t(2662),l=t(5495),o=t(7797),n=t(8964),i=t(8053);function d(){let{theme:e,setTheme:r}=(0,o.F)(),[t,d]=(0,n.useState)(!1);return t?(0,s.jsxs)(i.z,{variant:"ghost",size:"icon",onClick:function(){r("dark"===e?"light":"dark")},className:"relative h-9 w-9 overflow-hidden rounded-full transition-colors hover:bg-accent/80","aria-label":"Toggle theme",children:[s.jsx(a.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0"}),s.jsx(l.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100"}),s.jsx("span",{className:"sr-only",children:"Toggle theme"})]}):s.jsx(i.z,{variant:"ghost",size:"icon",className:"relative h-9 w-9 overflow-hidden rounded-full",children:s.jsx("span",{className:"sr-only",children:"Toggle theme"})})}},8053:(e,r,t)=>{"use strict";t.d(r,{d:()=>i,z:()=>d});var s=t(7247),a=t(8964),l=t(9008),o=t(7972),n=t(5008);let i=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-[0.98]",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:border-accent",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md",ghost:"hover:bg-accent/50 hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md hover:shadow-lg hover:from-blue-700 hover:to-indigo-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},d)=>{let c=a?l.g7:"button";return s.jsx(c,{className:(0,n.cn)(i({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"},3326:(e,r,t)=>{"use strict";t.d(r,{aM:()=>c,ue:()=>u,yo:()=>d});var s=t(7247),a=t(8964),l=t(400),o=t(7972),n=t(7013),i=t(5008);let d=l.fC,c=l.xz;l.x8;let m=l.h_,x=a.forwardRef(({className:e,...r},t)=>s.jsx(l.aV,{className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...r,ref:t}));x.displayName=l.aV.displayName;let p=(0,o.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),u=a.forwardRef(({side:e="right",className:r,children:t,...a},o)=>(0,s.jsxs)(m,{children:[s.jsx(x,{}),(0,s.jsxs)(l.VY,{ref:o,className:(0,i.cn)(p({side:e}),r),...a,children:[t,(0,s.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[s.jsx(n.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=l.VY.displayName,a.forwardRef(({className:e,...r},t)=>s.jsx(l.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold text-foreground",e),...r})).displayName=l.Dx.displayName,a.forwardRef(({className:e,...r},t)=>s.jsx(l.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r})).displayName=l.dk.displayName},1687:(e,r,t)=>{"use strict";let s,a;t.r(r),t.d(r,{default:()=>eZ,dynamic:()=>eW});var l=t(2051),o=t(5949),n=t.n(o),i=t(6269);function d(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var c=i.forwardRef((e,r)=>{let{children:t,...s}=e,a=i.Children.toArray(t),o=a.find(p);if(o){let e=o.props.children,t=a.map(r=>r!==o?r:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,l.jsx)(m,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,t):null})}return(0,l.jsx)(m,{...s,ref:r,children:t})});c.displayName="Slot";var m=i.forwardRef((e,r)=>{let{children:t,...s}=e;if(i.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t);return i.cloneElement(t,{...function(e,r){let t={...r};for(let s in r){let a=e[s],l=r[s];/^on[A-Z]/.test(s)?a&&l?t[s]=(...e)=>{l(...e),a(...e)}:a&&(t[s]=a):"style"===s?t[s]={...a,...l}:"className"===s&&(t[s]=[a,l].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props),ref:r?function(...e){return r=>{let t=!1,s=e.map(e=>{let s=d(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():d(e[r],null)}}}}(r,e):e})}return i.Children.count(t)>1?i.Children.only(null):null});m.displayName="SlotClone";var x=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function p(e){return i.isValidElement(e)&&e.type===x}function u(){for(var e,r,t=0,s="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,s,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r){if(Array.isArray(r)){var l=r.length;for(t=0;t<l;t++)r[t]&&(s=e(r[t]))&&(a&&(a+=" "),a+=s)}else for(s in r)r[s]&&(a&&(a+=" "),a+=s)}return a}(e))&&(s&&(s+=" "),s+=r);return s}let h=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,f=e=>{let r=v(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),g(t,r)||y(e)},getConflictingClassGroupIds:(e,r)=>{let a=t[e]||[];return r&&s[e]?[...a,...s[e]]:a}}},g=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],s=r.nextPart.get(t),a=s?g(e.slice(1),s):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},b=/^\[(.+)\]$/,y=e=>{if(b.test(e)){let r=b.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},v=e=>{let{theme:r,prefix:t}=e,s={nextPart:new Map,validators:[]};return k(Object.entries(e.classGroups),t).forEach(([e,t])=>{j(t,s,e,r)}),s},j=(e,r,t,s)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:w(r,e)).classGroupId=t;return}if("function"==typeof e){if(N(e)){j(e(s),r,t,s);return}r.validators.push({validator:e,classGroupId:t});return}Object.entries(e).forEach(([e,a])=>{j(a,w(r,e),t,s)})})},w=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},N=e=>e.isThemeGetter,k=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,z=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,s=new Map,a=(a,l)=>{t.set(a,l),++r>e&&(r=0,s=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=s.get(e))?(a(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):a(e,r)}}},C=e=>{let{separator:r,experimentalParseClassName:t}=e,s=1===r.length,a=r[0],l=r.length,o=e=>{let t;let o=[],n=0,i=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===n){if(c===a&&(s||e.slice(d,d+l)===r)){o.push(e.slice(i,d)),i=d+l;continue}if("/"===c){t=d;continue}}"["===c?n++:"]"===c&&n--}let d=0===o.length?e:e.substring(i),c=d.startsWith("!"),m=c?d.substring(1):d;return{modifiers:o,hasImportantModifier:c,baseClassName:m,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};return t?e=>t({className:e,parseClassName:o}):o},P=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},A=e=>({cache:z(e.cacheSize),parseClassName:C(e),...f(e)}),I=/\s+/,M=(e,r)=>{let{parseClassName:t,getClassGroupId:s,getConflictingClassGroupIds:a}=r,l=[],o=e.trim().split(I),n="";for(let e=o.length-1;e>=0;e-=1){let r=o[e],{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:m}=t(r),x=!!m,p=s(x?c.substring(0,m):c);if(!p){if(!x||!(p=s(c))){n=r+(n.length>0?" "+n:n);continue}x=!1}let u=P(i).join(":"),h=d?u+"!":u,f=h+p;if(l.includes(f))continue;l.push(f);let g=a(p,x);for(let e=0;e<g.length;++e){let r=g[e];l.push(h+r)}n=r+(n.length>0?" "+n:n)}return n};function _(){let e,r,t=0,s="";for(;t<arguments.length;)(e=arguments[t++])&&(r=R(e))&&(s&&(s+=" "),s+=r);return s}let R=e=>{let r;if("string"==typeof e)return e;let t="";for(let s=0;s<e.length;s++)e[s]&&(r=R(e[s]))&&(t&&(t+=" "),t+=r);return t},D=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},S=/^\[(?:([a-z-]+):)?(.+)\]$/i,q=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),G=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,L=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,F=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,T=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=e=>H(e)||E.has(e)||q.test(e),B=e=>es(e,"length",ea),H=e=>!!e&&!Number.isNaN(Number(e)),$=e=>es(e,"number",H),W=e=>!!e&&Number.isInteger(Number(e)),Z=e=>e.endsWith("%")&&H(e.slice(0,-1)),X=e=>S.test(e),Y=e=>G.test(e),U=new Set(["length","size","percentage"]),J=e=>es(e,U,el),Q=e=>es(e,"position",el),K=new Set(["image","url"]),ee=e=>es(e,K,en),er=e=>es(e,"",eo),et=()=>!0,es=(e,r,t)=>{let s=S.exec(e);return!!s&&(s[1]?"string"==typeof r?s[1]===r:r.has(s[1]):t(s[2]))},ea=e=>L.test(e)&&!F.test(e),el=()=>!1,eo=e=>T.test(e),en=e=>O.test(e);Symbol.toStringTag;let ei=function(e,...r){let t,s,a;let l=function(n){return s=(t=A(r.reduce((e,r)=>r(e),e()))).cache.get,a=t.cache.set,l=o,o(n)};function o(e){let r=s(e);if(r)return r;let l=M(e,t);return a(e,l),l}return function(){return l(_.apply(null,arguments))}}(()=>{let e=D("colors"),r=D("spacing"),t=D("blur"),s=D("brightness"),a=D("borderColor"),l=D("borderRadius"),o=D("borderSpacing"),n=D("borderWidth"),i=D("contrast"),d=D("grayscale"),c=D("hueRotate"),m=D("invert"),x=D("gap"),p=D("gradientColorStops"),u=D("gradientColorStopPositions"),h=D("inset"),f=D("margin"),g=D("opacity"),b=D("padding"),y=D("saturate"),v=D("scale"),j=D("sepia"),w=D("skew"),N=D("space"),k=D("translate"),z=()=>["auto","contain","none"],C=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto",X,r],A=()=>[X,r],I=()=>["",V,B],M=()=>["auto",H,X],_=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],S=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],q=()=>["start","end","center","between","around","evenly","stretch"],E=()=>["","0",X],G=()=>["auto","avoid","all","avoid-page","page","left","right","column"],L=()=>[H,X];return{cacheSize:500,separator:":",theme:{colors:[et],spacing:[V,B],blur:["none","",Y,X],brightness:L(),borderColor:[e],borderRadius:["none","","full",Y,X],borderSpacing:A(),borderWidth:I(),contrast:L(),grayscale:E(),hueRotate:L(),invert:E(),gap:A(),gradientColorStops:[e],gradientColorStopPositions:[Z,B],inset:P(),margin:P(),opacity:L(),padding:A(),saturate:L(),scale:L(),sepia:E(),skew:L(),space:A(),translate:A()},classGroups:{aspect:[{aspect:["auto","square","video",X]}],container:["container"],columns:[{columns:[Y]}],"break-after":[{"break-after":G()}],"break-before":[{"break-before":G()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[..._(),X]}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",W,X]}],basis:[{basis:P()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",X]}],grow:[{grow:E()}],shrink:[{shrink:E()}],order:[{order:["first","last","none",W,X]}],"grid-cols":[{"grid-cols":[et]}],"col-start-end":[{col:["auto",{span:["full",W,X]},X]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[et]}],"row-start-end":[{row:["auto",{span:[W,X]},X]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",X]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",X]}],gap:[{gap:[x]}],"gap-x":[{"gap-x":[x]}],"gap-y":[{"gap-y":[x]}],"justify-content":[{justify:["normal",...q()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...q(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...q(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[f]}],mx:[{mx:[f]}],my:[{my:[f]}],ms:[{ms:[f]}],me:[{me:[f]}],mt:[{mt:[f]}],mr:[{mr:[f]}],mb:[{mb:[f]}],ml:[{ml:[f]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",X,r]}],"min-w":[{"min-w":[X,r,"min","max","fit"]}],"max-w":[{"max-w":[X,r,"none","full","min","max","fit","prose",{screen:[Y]},Y]}],h:[{h:[X,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[X,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[X,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[X,r,"auto","min","max","fit"]}],"font-size":[{text:["base",Y,B]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",$]}],"font-family":[{font:[et]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",X]}],"line-clamp":[{"line-clamp":["none",H,$]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",V,X]}],"list-image":[{"list-image":["none",X]}],"list-style-type":[{list:["none","disc","decimal",X]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",V,B]}],"underline-offset":[{"underline-offset":["auto",V,X]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[..._(),Q]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",J]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ee]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[u]}],"gradient-via-pos":[{via:[u]}],"gradient-to-pos":[{to:[u]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[n]}],"border-w-x":[{"border-x":[n]}],"border-w-y":[{"border-y":[n]}],"border-w-s":[{"border-s":[n]}],"border-w-e":[{"border-e":[n]}],"border-w-t":[{"border-t":[n]}],"border-w-r":[{"border-r":[n]}],"border-w-b":[{"border-b":[n]}],"border-w-l":[{"border-l":[n]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[n]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[n]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:R()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[V,X]}],"outline-w":[{outline:[V,B]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[V,B]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Y,er]}],"shadow-color":[{shadow:[et]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...S(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":S()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[s]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",Y,X]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[m]}],saturate:[{saturate:[y]}],sepia:[{sepia:[j]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[s]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[j]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",X]}],duration:[{duration:L()}],ease:[{ease:["linear","in","out","in-out",X]}],delay:[{delay:L()}],animate:[{animate:["none","spin","ping","pulse","bounce",X]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[v]}],"scale-x":[{"scale-x":[v]}],"scale-y":[{"scale-y":[v]}],rotate:[{rotate:[W,X]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[w]}],"skew-y":[{"skew-y":[w]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[V,B,$]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function ed(...e){return ei(u(e))}let ec=(s="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-[0.98]",a={variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md hover:border-accent",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md",ghost:"hover:bg-accent/50 hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",gradient:"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md hover:shadow-lg hover:from-blue-700 hover:to-indigo-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}},e=>{var r;if((null==a?void 0:a.variants)==null)return u(s,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:t,defaultVariants:l}=a,o=Object.keys(t).map(r=>{let s=null==e?void 0:e[r],a=null==l?void 0:l[r];if(null===s)return null;let o=h(s)||h(a);return t[r][o]}),n=e&&Object.entries(e).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return u(s,o,null==a?void 0:null===(r=a.compoundVariants)||void 0===r?void 0:r.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...n}[r]):({...l,...n})[r]===t})?[...e,t,s]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)}),em=i.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...a},o)=>{let n=s?c:"button";return l.jsx(n,{className:ed(ec({variant:r,size:t,className:e})),ref:o,...a})});em.displayName="Button";let ex=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ep=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var eu={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let eh=(0,i.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:s,className:a="",children:l,iconNode:o,...n},d)=>(0,i.createElement)("svg",{ref:d,...eu,width:r,height:r,stroke:e,strokeWidth:s?24*Number(t)/Number(r):t,className:ep("lucide",a),...n},[...o.map(([e,r])=>(0,i.createElement)(e,r)),...Array.isArray(l)?l:[l]])),ef=(e,r)=>{let t=(0,i.forwardRef)(({className:t,...s},a)=>(0,i.createElement)(eh,{ref:a,iconNode:r,className:ep(`lucide-${ex(e)}`,t),...s}));return t.displayName=`${e}`,t},eg=ef("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),eb=ef("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function ey(){return(0,l.jsxs)("section",{className:"relative w-full overflow-hidden pt-4 pb-8 md:pt-6 md:pb-16 lg:pt-8 lg:pb-20",children:[l.jsx("div",{className:"absolute inset-0 -z-10 bg-gradient-to-b from-background/10 via-background/50 to-background opacity-50"}),l.jsx("div",{className:"absolute inset-0 -z-20 bg-[radial-gradient(circle_at_center,rgba(var(--primary-rgb),0.12),transparent_50%)]"}),l.jsx("div",{className:"absolute top-0 right-0 -z-10 h-[500px] w-[500px] rounded-full bg-primary/5 blur-3xl"}),l.jsx("div",{className:"absolute bottom-0 left-0 -z-10 h-[500px] w-[500px] rounded-full bg-primary/5 blur-3xl"}),l.jsx("div",{className:"container px-4 md:px-6",children:(0,l.jsxs)("div",{className:"grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]",children:[(0,l.jsxs)("div",{className:"flex flex-col justify-center space-y-8",children:[(0,l.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,l.jsxs)("div",{className:"inline-flex items-center rounded-full border bg-background px-3 py-1 text-sm",children:[l.jsx("span",{className:"flex h-2 w-2 rounded-full bg-primary"}),l.jsx("span",{className:"ml-2 text-foreground",children:"AI-Powered Career Documents"})]}),l.jsx("h1",{className:"text-3xl font-bold tracking-tighter sm:text-5xl md:text-6xl/none bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70",children:"Land Your Dream Job with CareerPilotAI"}),l.jsx("p",{className:"max-w-[600px] text-muted-foreground md:text-xl animate-slide-in-from-bottom",children:"Create professional resumes, cover letters, and LinkedIn bios tailored to your career goals in minutes with our AI-powered platform."}),l.jsx("div",{className:"flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 md:space-x-6",children:(0,l.jsxs)("ul",{className:"grid gap-2",children:[(0,l.jsxs)("li",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[l.jsx(eg,{className:"h-4 w-4 text-primary"}),l.jsx("span",{children:"Tailored to specific job positions"})]}),(0,l.jsxs)("li",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[l.jsx(eg,{className:"h-4 w-4 text-primary"}),l.jsx("span",{children:"Multiple export formats (PDF, DOCX, TXT)"})]}),(0,l.jsxs)("li",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[l.jsx(eg,{className:"h-4 w-4 text-primary"}),l.jsx("span",{children:"Professional templates and formatting"})]})]})})]}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-4 animate-fade-in [animation-delay:300ms]",children:[l.jsx(em,{asChild:!0,size:"lg",variant:"gradient",className:"group",children:(0,l.jsxs)(n(),{href:"/login",children:["Get Started Free",l.jsx(eb,{className:"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1"})]})}),l.jsx(em,{variant:"outline",size:"lg",asChild:!0,className:"backdrop-blur-sm",children:l.jsx(n(),{href:"#features",children:"See Features"})})]})]}),(0,l.jsxs)("div",{className:"relative hidden lg:block",children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-lg blur-xl -z-10"}),l.jsx("div",{className:"relative z-10 overflow-hidden rounded-lg border bg-background/80 backdrop-blur shadow-xl",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"space-y-2 mb-4",children:[l.jsx("h3",{className:"text-xl font-semibold",children:"Professional Resume"}),l.jsx("div",{className:"h-1 w-12 bg-primary rounded-full"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx("div",{className:"h-6 w-3/4 bg-muted rounded animate-pulse"}),l.jsx("div",{className:"h-6 w-1/2 bg-muted rounded animate-pulse"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx("div",{className:"h-4 w-full bg-muted/60 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-full bg-muted/60 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-3/4 bg-muted/60 rounded animate-pulse"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx("div",{className:"h-5 w-1/3 bg-muted/80 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-full bg-muted/60 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-full bg-muted/60 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-2/3 bg-muted/60 rounded animate-pulse"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx("div",{className:"h-5 w-1/3 bg-muted/80 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-full bg-muted/60 rounded animate-pulse"}),l.jsx("div",{className:"h-4 w-5/6 bg-muted/60 rounded animate-pulse"})]})]})]})}),l.jsx("div",{className:"absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 z-20",children:l.jsx("div",{className:"relative h-24 w-24 rounded-full border-4 border-background bg-primary/10 backdrop-blur flex items-center justify-center shadow-lg animate-bounce-slow",children:l.jsx("span",{className:"text-xs font-bold text-center",children:"AI-Powered"})})})]})]})})]})}let ev=ef("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),ej=ef("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]),ew=ef("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),eN=ef("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),ek=ef("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),ez=ef("FolderArchive",[["circle",{cx:"15",cy:"19",r:"2",key:"u2pros"}],["path",{d:"M20.9 19.8A2 2 0 0 0 22 18V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2h5.1",key:"1jj40k"}],["path",{d:"M15 11v-1",key:"cntcp"}],["path",{d:"M15 17v-2",key:"1279jj"}]]),eC=ef("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),eP=ef("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),eA=ef("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);function eI(){let e=[{icon:l.jsx(ev,{className:"h-6 w-6 text-primary"}),title:"AI Resume Builder",description:"Create professional resumes tailored to specific job positions with our Gemini-powered AI generator."},{icon:l.jsx(ej,{className:"h-6 w-6 text-primary"}),title:"Cover Letter Generator",description:"Generate personalized cover letters that highlight your skills and experience for any job application."},{icon:l.jsx(ew,{className:"h-6 w-6 text-primary"}),title:"LinkedIn Bio Creator",description:"Craft engaging LinkedIn bios that showcase your professional brand and attract recruiters."},{icon:l.jsx(eN,{className:"h-6 w-6 text-primary"}),title:"Multiple Export Formats",description:"Export your documents in PDF, DOCX, or TXT formats based on your subscription plan."},{icon:l.jsx(ek,{className:"h-6 w-6 text-primary"}),title:"Premium Templates",description:"Access professionally designed templates with our Basic and Premium plans for standout documents."},{icon:l.jsx(ez,{className:"h-6 w-6 text-primary"}),title:"Document Management",description:"Store, organize, and access all your career documents in one secure location."}],r=[{number:"01",title:"Create an Account",description:"Sign up for free and get instant access to our AI-powered document generation tools."},{number:"02",title:"Enter Your Details",description:"Fill in your professional information, skills, and experience in our user-friendly forms."},{number:"03",title:"Generate Documents",description:"Our AI instantly creates tailored resumes, cover letters, and LinkedIn bios based on your input."},{number:"04",title:"Export & Apply",description:"Download your documents in your preferred format and start applying for jobs with confidence."}];return(0,l.jsxs)("section",{id:"features",className:"relative w-full py-8 md:py-16 lg:py-24 overflow-hidden",children:[l.jsx("div",{className:"absolute inset-0 -z-10 bg-muted/40"}),l.jsx("div",{className:"absolute inset-0 -z-10 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)] dark:bg-[radial-gradient(#1f2937_1px,transparent_1px)]"}),(0,l.jsxs)("div",{className:"container px-4 md:px-6",children:[l.jsx("div",{className:"flex flex-col items-center justify-center space-y-4 text-center",children:(0,l.jsxs)("div",{className:"space-y-2 animate-fade-in",children:[l.jsx("div",{className:"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm font-medium text-primary",children:"Features"}),l.jsx("h2",{className:"text-3xl font-bold tracking-tighter md:text-4xl/tight bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70",children:"Everything You Need for Your Job Search"}),l.jsx("p",{className:"mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-in [animation-delay:200ms]",children:"CareerPilotAI provides all the tools you need to create professional career documents and land your dream job."})]})}),l.jsx("div",{className:"mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3",children:e.map((e,r)=>(0,l.jsxs)("div",{className:ed("group flex flex-col items-center space-y-4 rounded-lg border p-6 transition-all duration-200 hover:shadow-md hover:border-primary/20 hover:bg-accent/50","animate-fade-in [animation-delay:var(--delay)]"),style:{"--delay":`${(r+1)*100}ms`},children:[l.jsx("div",{className:"rounded-full bg-primary/10 p-4 transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/20",children:e.icon}),l.jsx("h3",{className:"text-xl font-bold",children:e.title}),l.jsx("p",{className:"text-center text-muted-foreground",children:e.description})]},r))}),(0,l.jsxs)("div",{className:"mt-20",children:[l.jsx("div",{className:"flex flex-col items-center justify-center space-y-4 text-center",children:(0,l.jsxs)("div",{className:"space-y-2 animate-fade-in",children:[l.jsx("div",{className:"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm font-medium text-primary",children:"How It Works"}),l.jsx("h2",{className:"text-3xl font-bold tracking-tighter md:text-4xl/tight bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70",children:"Simple Process, Powerful Results"}),l.jsx("p",{className:"mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed animate-fade-in [animation-delay:200ms]",children:"Get started in minutes and transform your job application process"})]})}),l.jsx("div",{className:"mt-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4",children:r.map((e,t)=>(0,l.jsxs)("div",{className:"relative flex flex-col items-center p-6 animate-fade-in [animation-delay:var(--delay)]",style:{"--delay":`${(t+1)*150}ms`},children:[t<r.length-1&&l.jsx("div",{className:"absolute top-8 left-[calc(50%)] h-0.5 w-[calc(100%-2rem)] bg-gradient-to-r from-primary/50 to-transparent hidden lg:block"}),(0,l.jsxs)("div",{className:"relative mb-4",children:[l.jsx("div",{className:"absolute inset-0 rounded-full bg-primary/20 blur-sm"}),l.jsx("div",{className:"relative z-10 flex h-16 w-16 items-center justify-center rounded-full border-2 border-primary/50 bg-background text-2xl font-bold text-primary",children:e.number})]}),l.jsx("h3",{className:"mb-2 text-xl font-bold",children:e.title}),l.jsx("p",{className:"text-center text-muted-foreground",children:e.description})]},t))})]}),(0,l.jsxs)("div",{className:"mt-24 grid grid-cols-1 gap-8 md:grid-cols-3",children:[(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-4 rounded-lg border p-6 transition-all duration-200 hover:shadow-md animate-fade-in",children:[l.jsx(eC,{className:"h-10 w-10 text-primary"}),l.jsx("h3",{className:"text-xl font-bold",children:"Fast & Efficient"}),l.jsx("p",{className:"text-center text-muted-foreground",children:"Generate professional documents in seconds, not hours. Save time and focus on your job search."})]}),(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-4 rounded-lg border p-6 transition-all duration-200 hover:shadow-md animate-fade-in [animation-delay:150ms]",children:[l.jsx(eP,{className:"h-10 w-10 text-primary"}),l.jsx("h3",{className:"text-xl font-bold",children:"Secure & Private"}),l.jsx("p",{className:"text-center text-muted-foreground",children:"Your data is encrypted and never shared. We prioritize your privacy and security."})]}),(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-4 rounded-lg border p-6 transition-all duration-200 hover:shadow-md animate-fade-in [animation-delay:300ms]",children:[l.jsx(eA,{className:"h-10 w-10 text-primary"}),l.jsx("h3",{className:"text-xl font-bold",children:"Always Available"}),l.jsx("p",{className:"text-center text-muted-foreground",children:"Access your documents anytime, anywhere. Make updates and generate new versions whenever needed."})]})]})]})]})}let eM=i.forwardRef(({className:e,...r},t)=>l.jsx("div",{ref:t,className:ed("rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md",e),...r}));eM.displayName="Card";let e_=i.forwardRef(({className:e,...r},t)=>l.jsx("div",{ref:t,className:ed("flex flex-col space-y-1.5 p-6",e),...r}));e_.displayName="CardHeader";let eR=i.forwardRef(({className:e,...r},t)=>l.jsx("div",{ref:t,className:ed("text-2xl font-semibold leading-none tracking-tight",e),...r}));eR.displayName="CardTitle";let eD=i.forwardRef(({className:e,...r},t)=>l.jsx("div",{ref:t,className:ed("text-sm text-muted-foreground",e),...r}));eD.displayName="CardDescription";let eS=i.forwardRef(({className:e,...r},t)=>l.jsx("div",{ref:t,className:ed("p-6 pt-0",e),...r}));eS.displayName="CardContent";let eq=i.forwardRef(({className:e,...r},t)=>l.jsx("div",{ref:t,className:ed("flex items-center p-6 pt-0",e),...r}));eq.displayName="CardFooter";let eE=ef("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),eG=ef("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),eL={apiBaseUrl:"https://careerpilotai-2.onrender.com/api",app:{name:"CareerPilotAI",version:"1.0.0"},pricing:{basic:parseInt("499"),premium:parseInt("999")},isDevelopment:!1,isProduction:!0};function eF(){return l.jsx("section",{id:"pricing",className:"w-full py-8 md:py-16 lg:py-24",children:(0,l.jsxs)("div",{className:"container px-4 md:px-6",children:[l.jsx("div",{className:"flex flex-col items-center justify-center space-y-4 text-center",children:(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx("div",{className:"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm font-medium text-primary",children:"Pricing"}),l.jsx("h2",{className:"text-3xl font-bold tracking-tighter md:text-4xl/tight bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70",children:"Choose the Right Plan for Your Career"}),l.jsx("p",{className:"mx-auto max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed",children:"Affordable plans for every stage of your career journey. All prices in Indian Rupees (₹)."})]})}),(0,l.jsxs)("div",{className:"mx-auto grid max-w-5xl grid-cols-1 gap-6 py-8 md:grid-cols-3",children:[(0,l.jsxs)(eM,{className:"relative overflow-hidden transition-all duration-300 hover:shadow-md flex flex-col",children:[l.jsx("div",{className:"absolute inset-x-0 top-0 h-2 bg-gradient-to-r from-primary/40 to-primary/10"}),(0,l.jsxs)(e_,{className:"flex-shrink-0 pb-4",children:[l.jsx(eR,{children:"Free"}),(0,l.jsxs)("div",{className:"text-3xl font-bold",children:["₹0",l.jsx("span",{className:"text-sm font-normal text-muted-foreground",children:"/month"})]}),l.jsx(eD,{children:"Perfect for trying out our services."})]}),l.jsx(eS,{className:"flex-grow flex flex-col py-2",children:(0,l.jsxs)("ul",{className:"space-y-1 text-sm flex-grow",children:[(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"1 Resume Generation"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"1 Cover Letter Generation"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"1 LinkedIn Bio Generation"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Basic Templates"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"PDF Export"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eG,{className:"mr-2 h-4 w-4 text-muted-foreground flex-shrink-0"}),l.jsx("span",{className:"text-muted-foreground",children:"DOCX Export"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"TXT Export"})]})]})}),l.jsx(eq,{children:l.jsx(em,{className:"w-full",asChild:!0,children:l.jsx(n(),{href:"/login",children:"Get Started"})})})]}),(0,l.jsxs)(eM,{className:"relative overflow-hidden border-primary transition-all duration-300 hover:shadow-md flex flex-col",children:[l.jsx("div",{className:"absolute inset-x-0 top-0 h-2 bg-gradient-to-r from-primary to-primary/60"}),(0,l.jsxs)(e_,{className:"flex-shrink-0 pb-4",children:[l.jsx("div",{className:"inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground",children:"Popular"}),l.jsx(eR,{children:"Basic"}),(0,l.jsxs)("div",{className:"text-3xl font-bold",children:["₹",eL.pricing.basic,l.jsx("span",{className:"text-sm font-normal text-muted-foreground",children:"/month"})]}),l.jsx(eD,{children:"For active job seekers."})]}),l.jsx(eS,{className:"flex-grow flex flex-col py-2",children:(0,l.jsxs)("ul",{className:"space-y-1 text-sm flex-grow",children:[(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"5 Resume Generations"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"5 Cover Letter Generations"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"3 LinkedIn Bio Generations"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Premium Templates"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"PDF Export"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"DOCX Export"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"TXT Export"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Priority Support"})]})]})}),l.jsx(eq,{children:l.jsx(em,{className:"w-full",asChild:!0,children:l.jsx(n(),{href:"/login",children:"Subscribe"})})})]}),(0,l.jsxs)(eM,{className:"relative overflow-hidden transition-all duration-300 hover:shadow-md flex flex-col",children:[l.jsx("div",{className:"absolute inset-x-0 top-0 h-2 bg-gradient-to-r from-primary/80 via-primary to-primary/80"}),(0,l.jsxs)(e_,{className:"flex-shrink-0 pb-4",children:[l.jsx(eR,{children:"Premium"}),(0,l.jsxs)("div",{className:"text-3xl font-bold",children:["₹",eL.pricing.premium,l.jsx("span",{className:"text-sm font-normal text-muted-foreground",children:"/month"})]}),l.jsx(eD,{children:"For professionals and power users."})]}),l.jsx(eS,{className:"flex-grow flex flex-col py-2",children:(0,l.jsxs)("ul",{className:"space-y-1 text-sm flex-grow",children:[(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Unlimited Resume Generations"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Unlimited Cover Letter Generations"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Unlimited LinkedIn Bio Generations"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"All Premium Templates"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"All Export Formats"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Priority Support"})]}),(0,l.jsxs)("li",{className:"flex items-center py-0.5",children:[l.jsx(eE,{className:"mr-2 h-4 w-4 text-primary flex-shrink-0"}),l.jsx("span",{children:"Advanced AI Features"})]})]})}),l.jsx(eq,{children:l.jsx(em,{className:"w-full",asChild:!0,children:l.jsx(n(),{href:"/login",children:"Subscribe"})})})]})]}),l.jsx("div",{className:"mt-12 text-center",children:(0,l.jsxs)("p",{className:"text-muted-foreground",children:["All plans include secure document storage and access to our AI-powered generation tools.",l.jsx("br",{}),"Need help choosing? ",l.jsx(n(),{href:"/contact",className:"text-primary underline underline-offset-4 hover:text-primary/80",children:"Contact us"})," for assistance."]})})]})})}let eT=ef("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),eO=ef("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),eV=ef("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),eB=ef("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);function eH(){return l.jsx("footer",{className:"w-full border-t py-12 md:py-16",children:(0,l.jsxs)("div",{className:"container px-4 md:px-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[l.jsx(eT,{className:"h-6 w-6 text-primary"}),l.jsx("span",{className:"font-bold",children:"CareerPilotAI"})]}),l.jsx("p",{className:"text-sm text-muted-foreground",children:"AI-powered career document generation platform to help you land your dream job."}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[l.jsx(eO,{className:"h-4 w-4"}),l.jsx("span",{children:"Bengaluru, India"})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-sm font-medium",children:"Product"}),(0,l.jsxs)("ul",{className:"space-y-2 text-sm",children:[l.jsx("li",{children:l.jsx(n(),{href:"/#features",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Features"})}),l.jsx("li",{children:l.jsx(n(),{href:"/#pricing",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Pricing"})}),l.jsx("li",{children:l.jsx(n(),{href:"/about",className:"text-muted-foreground transition-colors hover:text-foreground",children:"About"})})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-sm font-medium",children:"Resources"}),(0,l.jsxs)("ul",{className:"space-y-2 text-sm",children:[l.jsx("li",{children:l.jsx(n(),{href:"/blog",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Blog"})}),l.jsx("li",{children:l.jsx(n(),{href:"/faq",className:"text-muted-foreground transition-colors hover:text-foreground",children:"FAQ"})}),l.jsx("li",{children:l.jsx(n(),{href:"/support",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Support"})})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("h3",{className:"text-sm font-medium",children:"Legal"}),(0,l.jsxs)("ul",{className:"space-y-2 text-sm",children:[l.jsx("li",{children:l.jsx(n(),{href:"/terms",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Terms of Service"})}),l.jsx("li",{children:l.jsx(n(),{href:"/privacy",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Privacy Policy"})}),l.jsx("li",{children:l.jsx(n(),{href:"/contact",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Contact Us"})})]})]})]}),(0,l.jsxs)("div",{className:"mt-12 flex flex-col items-center justify-between gap-4 border-t pt-8 md:flex-row",children:[l.jsx("p",{className:"text-center text-sm text-muted-foreground md:text-left",children:"\xa9 2024 CareerPilotAI. All rights reserved."}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsxs)(n(),{href:"mailto:<EMAIL>",className:"text-muted-foreground hover:text-foreground",children:[l.jsx(eV,{className:"h-5 w-5"}),l.jsx("span",{className:"sr-only",children:"Email"})]}),(0,l.jsxs)(n(),{href:"tel:+919876543210",className:"text-muted-foreground hover:text-foreground",children:[l.jsx(eB,{className:"h-5 w-5"}),l.jsx("span",{className:"sr-only",children:"Phone"})]})]})]})]})})}let e$=(0,t(5347).createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\components\landing-header.tsx#LandingHeader`),eW="force-dynamic";function eZ(){return(0,l.jsxs)("div",{className:"flex min-h-screen flex-col",children:[l.jsx(e$,{}),(0,l.jsxs)("main",{className:"flex-1",children:[l.jsx(ey,{}),l.jsx(eI,{}),l.jsx(eF,{})]}),l.jsx(eH,{})]})}},5949:(e,r,t)=>{"use strict";let{createProxy:s}=t(5347);e.exports=s("D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\node_modules\\next\\dist\\client\\link.js")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[475,97,43],()=>t(5606));module.exports=s})();