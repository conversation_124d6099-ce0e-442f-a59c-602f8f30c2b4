{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [], "headers": [], "dynamicRoutes": [{"page": "/document/[type]/[id]", "regex": "^/document/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtype": "nxtPtype", "nxtPid": "nxtPid"}, "namedRegex": "^/document/(?<nxtPtype>[^/]+?)/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/404", "regex": "^/404(?:/)?$", "routeKeys": {}, "namedRegex": "^/404(?:/)?$"}, {"page": "/500", "regex": "^/500(?:/)?$", "routeKeys": {}, "namedRegex": "^/500(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/cover-letters", "regex": "^/cover\\-letters(?:/)?$", "routeKeys": {}, "namedRegex": "^/cover\\-letters(?:/)?$"}, {"page": "/cover-letters-test", "regex": "^/cover\\-letters\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/cover\\-letters\\-test(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/linkedin-bios", "regex": "^/linkedin\\-bios(?:/)?$", "routeKeys": {}, "namedRegex": "^/linkedin\\-bios(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/resumes", "regex": "^/resumes(?:/)?$", "routeKeys": {}, "namedRegex": "^/resumes(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/subscription", "regex": "^/subscription(?:/)?$", "routeKeys": {}, "namedRegex": "^/subscription(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "skipMiddlewareUrlNormalize": true, "rewrites": []}