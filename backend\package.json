{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "docx": "^9.5.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.2", "openai": "^4.98.0", "puppeteer": "^24.8.2", "sanitize-html": "^2.17.0", "stripe": "^18.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}