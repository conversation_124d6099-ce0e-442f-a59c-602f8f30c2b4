(()=>{var e={};e.id=903,e.ids=[903],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},9632:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d}),a(5940),a(2626),a(546);var t=a(170),s=a(5002),i=a(3876),n=a.n(i),o=a(6299),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(r,l);let d=["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5940)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\dashboard\\page.tsx"],m="/(dashboard)/dashboard/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2868:(e,r,a)=>{Promise.resolve().then(a.bind(a,5466))},5466:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>w,dynamic:()=>j});var t=a(7247),s=a(8964),i=a(9906),n=a(4178),o=a(7734),l=a(9767),d=a(5503),c=a(8053),m=a(4615),u=a(6903),h=a(9379),x=a(8749),p=a(8339),g=a(5497),f=a(4445),v=a(906),b=a(7757);let j="force-dynamic";function N({user:e,totalDocuments:r,isLoading:a,onCreateDocument:s,setActiveTab:i}){return(0,t.jsxs)(b.Zb,{className:"overflow-hidden border-muted-foreground/20 transition-all duration-300 hover:shadow-md",children:[(0,t.jsxs)(b.Ol,{className:"bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950/50 dark:to-slate-900/30 pb-4",children:[(0,t.jsxs)(b.ll,{className:"flex items-center gap-2 text-slate-700 dark:text-slate-300",children:[t.jsx(m.Z,{className:"h-5 w-5 transition-transform duration-300 hover:rotate-90"}),"Quick Start"]}),t.jsx(b.SZ,{children:"Get started with your career documents"})]}),t.jsx(b.aY,{className:"pt-4",children:a?t.jsx("div",{className:"h-24 animate-pulse rounded-md bg-muted"}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground animate-fade-in",children:["You have created a total of ",t.jsx("span",{className:"font-bold",children:r})," documents so far. Create more documents to enhance your job search."]}),(0,t.jsxs)("div",{className:"grid gap-2 md:grid-cols-3",children:[(0,t.jsxs)(c.z,{variant:"outline",size:"sm",className:"gap-1 justify-start transition-all duration-300 hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/20 dark:hover:text-blue-300 hover:translate-y-[-2px] animate-fade-in",style:{animationDelay:"100ms"},onClick:()=>{i("resume"),s()},children:[t.jsx(u.Z,{className:"h-4 w-4 mr-1 transition-transform duration-300 group-hover:scale-110"}),"New Resume"]}),(0,t.jsxs)(c.z,{variant:"outline",size:"sm",className:"gap-1 justify-start transition-all duration-300 hover:bg-purple-50 hover:text-purple-700 dark:hover:bg-purple-900/20 dark:hover:text-purple-300 hover:translate-y-[-2px] animate-fade-in",style:{animationDelay:"200ms"},onClick:()=>{i("coverLetter"),s()},children:[t.jsx(h.Z,{className:"h-4 w-4 mr-1 transition-transform duration-300 group-hover:scale-110"}),"New Cover Letter"]}),(0,t.jsxs)(c.z,{variant:"outline",size:"sm",className:"gap-1 justify-start transition-all duration-300 hover:bg-indigo-50 hover:text-indigo-700 dark:hover:bg-indigo-900/20 dark:hover:text-indigo-300 hover:translate-y-[-2px] animate-fade-in",style:{animationDelay:"300ms"},onClick:()=>{i("linkedin"),s()},children:[t.jsx(u.Z,{className:"h-4 w-4 mr-1 transition-transform duration-300 group-hover:scale-110"}),"New LinkedIn Bio"]})]})]})})]})}function y(){let{isAuthenticated:e,loading:r,user:a}=(0,o.a)(),j=(0,n.useSearchParams)().get("tab"),[y,w]=(0,s.useState)(j||"resume"),[k,Z]=(0,s.useState)(!1),[C,P]=(0,s.useState)([]),[D,A]=(0,s.useState)([]),[L,I]=(0,s.useState)([]),[q,z]=(0,s.useState)(!0),[S,_]=(0,s.useState)(!1),{toast:T}=(0,v.pm)(),Y=async()=>{z(!0),_(!0);try{let[e,r,a]=await Promise.all([f.md.getAllResumes(),f.Gi.getAllCoverLetters(),f.bc.getAllLinkedInBios()]),t=e.data.resumes||e.data.data||[],s=r.data.coverLetters||r.data.data||[],i=a.data.linkedinBios||a.data.data||[];P(t),A(s),I(i)}catch(e){console.error("Error fetching documents:",e),T({title:"Error fetching documents",description:"Please try again later",variant:"destructive"})}finally{z(!1),_(!1)}};return r?t.jsx("div",{className:"flex h-screen items-center justify-center bg-gradient-to-b from-background to-background/90",children:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-4 animate-fade-in",children:[(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute -inset-4 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 opacity-20 blur-lg animate-pulse"}),t.jsx(x.Z,{className:"h-12 w-12 animate-spin text-primary relative"})]}),t.jsx("p",{className:"text-sm text-muted-foreground animate-pulse",children:"Loading your dashboard..."})]})}):(0,t.jsxs)(l.r,{children:[t.jsx(d.x,{heading:`Welcome${a?.name?", "+a.name.split(" ")[0]:""}`,text:"Create and manage your career documents",className:"animate-fade-in",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 animate-fade-in [animation-delay:200ms]",children:[(0,t.jsxs)(c.z,{variant:"outline",size:"icon",onClick:Y,disabled:S,className:"h-9 w-9 transition-all duration-300 hover:bg-primary/10 hover:border-primary/30",children:[t.jsx(p.Z,{className:`h-4 w-4 ${S?"animate-spin":"transition-transform duration-300 hover:rotate-180"}`}),t.jsx("span",{className:"sr-only",children:"Refresh"})]}),(0,t.jsxs)(c.z,{onClick:()=>Z(!0),className:"gap-1 transition-all duration-300 hover:scale-105",children:[t.jsx(m.Z,{className:"mr-1 h-4 w-4 transition-transform duration-300 group-hover:rotate-90"}),"New Document"]})]})}),t.jsx("div",{className:"mt-8 animate-fade-in [animation-delay:300ms]",children:t.jsx(N,{user:a,totalDocuments:C.length+D.length+L.length,isLoading:q,onCreateDocument:()=>Z(!0),setActiveTab:w})}),t.jsx("div",{className:"mt-8 animate-fade-in [animation-delay:400ms]",children:(0,t.jsxs)(b.Zb,{className:"overflow-hidden border-muted-foreground/20 transition-all duration-300 hover:shadow-md",children:[(0,t.jsxs)(b.Ol,{className:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/30 pb-4",children:[(0,t.jsxs)(b.ll,{className:"flex items-center gap-2 text-green-700 dark:text-green-300",children:[t.jsx(p.Z,{className:"h-5 w-5 animate-pulse"}),"Recent Activity"]}),t.jsx(b.SZ,{children:"Your latest document updates"})]}),t.jsx(b.aY,{className:"pt-4",children:q?(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx("div",{className:"h-12 animate-pulse rounded-md bg-muted"}),t.jsx("div",{className:"h-12 animate-pulse rounded-md bg-muted"})]}):t.jsx("div",{className:"space-y-4",children:C.length+D.length+L.length>0?t.jsx("div",{className:"space-y-2",children:[...C.map(e=>({...e,docType:"resume"})),...D.map(e=>({...e,docType:"coverLetter"})),...L.map(e=>({...e,docType:"linkedin"}))].sort((e,r)=>new Date(r.updatedAt||r.createdAt).getTime()-new Date(e.updatedAt||e.createdAt).getTime()).slice(0,3).map((e,r)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-md hover:bg-muted/50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary/10",children:"resume"===e.docType?t.jsx(u.Z,{className:"h-4 w-4 text-primary"}):"coverLetter"===e.docType?t.jsx(h.Z,{className:"h-4 w-4 text-primary"}):t.jsx(u.Z,{className:"h-4 w-4 text-primary"})}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm font-medium",children:e.title}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Updated ",new Date(e.updatedAt||e.createdAt).toLocaleDateString()]})]})]}),t.jsx(c.z,{variant:"ghost",size:"sm",asChild:!0,children:t.jsx(i.default,{href:`/document/${"resume"===e.docType?"resume":"coverLetter"===e.docType?"cover-letter":"linkedin"}/${e.id}`,children:"View"})})]},r))}):(0,t.jsxs)("div",{className:"text-center py-4",children:[t.jsx("p",{className:"text-sm text-muted-foreground",children:"No documents created yet."}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Create your first document to get started!"})]})})})]})}),(0,t.jsxs)("div",{className:"grid gap-6 mt-10 md:grid-cols-2 lg:grid-cols-3 animate-fade-in [animation-delay:500ms]",children:[(0,t.jsxs)(b.Zb,{className:"overflow-hidden border-muted-foreground/20 group transition-all duration-300 hover:shadow-lg hover:border-primary/20 hover:scale-[1.02] animate-fade-in [animation-delay:600ms]",children:[(0,t.jsxs)(b.Ol,{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 pb-2",children:[(0,t.jsxs)(b.ll,{className:"flex items-center gap-2 text-blue-700 dark:text-blue-300",children:[t.jsx(u.Z,{className:"h-5 w-5 transition-transform duration-300 group-hover:scale-110"}),"Resumes"]}),t.jsx(b.SZ,{children:"Professional summaries for job applications"})]}),(0,t.jsxs)(b.aY,{className:"pt-4 pb-2",children:[t.jsx("div",{className:"text-2xl font-bold mb-2 transition-all duration-300 group-hover:text-primary",children:C.length}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[1===C.length?"resume":"resumes"," created"]})]}),t.jsx(b.eW,{className:"border-t p-4 bg-muted/5",children:t.jsx(c.z,{variant:"outline",size:"sm",className:"w-full gap-1 group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300",asChild:!0,children:(0,t.jsxs)(i.default,{href:"/resumes",children:[t.jsx(u.Z,{className:"mr-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"}),"View All Resumes"]})})})]}),(0,t.jsxs)(b.Zb,{className:"overflow-hidden border-muted-foreground/20 group transition-all duration-300 hover:shadow-lg hover:border-primary/20 hover:scale-[1.02] animate-fade-in [animation-delay:700ms]",children:[(0,t.jsxs)(b.Ol,{className:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 pb-2",children:[(0,t.jsxs)(b.ll,{className:"flex items-center gap-2 text-purple-700 dark:text-purple-300",children:[t.jsx(h.Z,{className:"h-5 w-5 transition-transform duration-300 group-hover:scale-110"}),"Cover Letters"]}),t.jsx(b.SZ,{children:"Tailored letters for job applications"})]}),(0,t.jsxs)(b.aY,{className:"pt-4 pb-2",children:[t.jsx("div",{className:"text-2xl font-bold mb-2 transition-all duration-300 group-hover:text-primary",children:D.length}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[1===D.length?"cover letter":"cover letters"," created"]})]}),t.jsx(b.eW,{className:"border-t p-4 bg-muted/5",children:t.jsx(c.z,{variant:"outline",size:"sm",className:"w-full gap-1 group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300",asChild:!0,children:(0,t.jsxs)(i.default,{href:"/cover-letters",children:[t.jsx(h.Z,{className:"mr-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"}),"View All Cover Letters"]})})})]}),(0,t.jsxs)(b.Zb,{className:"overflow-hidden border-muted-foreground/20 group transition-all duration-300 hover:shadow-lg hover:border-primary/20 hover:scale-[1.02] md:col-span-2 lg:col-span-1 animate-fade-in [animation-delay:800ms]",children:[(0,t.jsxs)(b.Ol,{className:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/50 dark:to-indigo-900/30 pb-2",children:[(0,t.jsxs)(b.ll,{className:"flex items-center gap-2 text-indigo-700 dark:text-indigo-300",children:[t.jsx(u.Z,{className:"h-5 w-5 transition-transform duration-300 group-hover:scale-110"}),"LinkedIn Bios"]}),t.jsx(b.SZ,{children:"Professional profiles for LinkedIn"})]}),(0,t.jsxs)(b.aY,{className:"pt-4 pb-2",children:[t.jsx("div",{className:"text-2xl font-bold mb-2 transition-all duration-300 group-hover:text-primary",children:L.length}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:[1===L.length?"LinkedIn bio":"LinkedIn bios"," created"]})]}),t.jsx(b.eW,{className:"border-t p-4 bg-muted/5",children:t.jsx(c.z,{variant:"outline",size:"sm",className:"w-full gap-1 group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300",asChild:!0,children:(0,t.jsxs)(i.default,{href:"/linkedin-bios",children:[t.jsx(u.Z,{className:"mr-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"}),"View All LinkedIn Bios"]})})})]})]}),t.jsx("div",{className:"mt-6",children:t.jsx(g.g,{open:k,onOpenChange:Z,documentType:y,onDocumentCreated:()=>{Y(),Z(!1),T({title:"Document created",description:"Your document has been created successfully",key:"document-created-"+new Date().getTime()})},className:"animate-fade-in"})})]})}function w(){return t.jsx(s.Suspense,{fallback:t.jsx("div",{className:"flex h-screen items-center justify-center bg-gradient-to-b from-background to-background/90",children:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-4 animate-fade-in",children:[(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute -inset-4 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 opacity-20 blur-lg animate-pulse"}),t.jsx(x.Z,{className:"h-12 w-12 animate-spin text-primary relative"})]}),t.jsx("p",{className:"text-sm text-muted-foreground animate-pulse",children:"Loading your dashboard..."})]})}),children:t.jsx(y,{})})}},5940:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i,dynamic:()=>s});var t=a(5347);let s=(0,t.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\dashboard\page.tsx#dynamic`),i=(0,t.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\dashboard\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[475,97,12,166,43,967,445],()=>a(9632));module.exports=t})();