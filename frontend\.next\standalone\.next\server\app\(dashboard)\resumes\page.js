(()=>{var e={};e.id=698,e.ids=[698],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},9793:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>o}),s(3791),s(2626),s(546);var r=s(170),a=s(5002),i=s(3876),n=s.n(i),l=s(6299),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["(dashboard)",{children:["resumes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3791)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\resumes\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\resumes\\page.tsx"],u="/(dashboard)/resumes/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/resumes/page",pathname:"/resumes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6382:(e,t,s)=>{Promise.resolve().then(s.bind(s,1710))},1710:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y,dynamic:()=>w});var r=s(7247),a=s(8964),i=s(7734),n=s(9767),l=s(5503),d=s(223),o=s(8053),c=s(6903),u=s(8749),m=s(8339),p=s(4615),x=s(5497),h=s(4445),g=s(906),f=s(7757),j=s(6991),v=s(4049);let w="force-dynamic";function b({count:e,isLoading:t}){return(0,r.jsxs)(f.Zb,{className:"overflow-hidden border-muted-foreground/20",children:[(0,r.jsxs)(f.Ol,{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 pb-2",children:[(0,r.jsxs)(f.ll,{className:"flex items-center gap-2 text-blue-700 dark:text-blue-300",children:[r.jsx(c.Z,{className:"h-5 w-5"}),"Resumes"]}),r.jsx(f.SZ,{children:"Your professional summaries"})]}),r.jsx(f.aY,{className:"pt-4",children:t?r.jsx("div",{className:"h-8 w-16 animate-pulse rounded-md bg-muted"}):r.jsx("div",{className:"text-2xl font-bold",children:e})})]})}function y(){let{isAuthenticated:e,loading:t}=(0,i.a)(),[s,f]=(0,a.useState)(!1),[w,y]=(0,a.useState)([]),[N,A]=(0,a.useState)(!0),[C,P]=(0,a.useState)(!1),[D,q]=(0,a.useState)(""),[S,_]=(0,a.useState)("newest"),{toast:I}=(0,g.pm)(),T=async()=>{A(!0),P(!0);try{let e=await h.md.getAllResumes(),t=e.data.resumes||e.data.data||[];y(t)}catch(e){console.error("Error fetching resumes:",e),I({title:"Error fetching resumes",description:"Please try again later",variant:"destructive"})}finally{A(!1),P(!1)}},R=()=>{f(!0)},k=async e=>{try{await h.md.deleteResume(e),y(w.filter(t=>t.id!==e)),I({title:"Resume deleted",description:"Your resume has been deleted successfully"})}catch(e){I({title:"Error deleting resume",description:"Please try again later",variant:"destructive"})}},Z=[...w.filter(e=>e.title.toLowerCase().includes(D.toLowerCase())||e.jobTitle&&e.jobTitle.toLowerCase().includes(D.toLowerCase())||e.company&&e.company.toLowerCase().includes(D.toLowerCase()))].sort((e,t)=>{try{switch(S){case"newest":let s=e.updatedAt?new Date(e.updatedAt).getTime():new Date(e.createdAt).getTime();return(t.updatedAt?new Date(t.updatedAt).getTime():new Date(t.createdAt).getTime())-s;case"oldest":let r=e.updatedAt?new Date(e.updatedAt).getTime():new Date(e.createdAt).getTime(),a=t.updatedAt?new Date(t.updatedAt).getTime():new Date(t.createdAt).getTime();return r-a;case"a-z":return(e.title||"").localeCompare(t.title||"");case"z-a":return(t.title||"").localeCompare(e.title||"");default:return 0}}catch(e){return console.error("Error sorting resumes:",e),0}});return t?r.jsx("div",{className:"flex h-screen items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[r.jsx(u.Z,{className:"h-10 w-10 animate-spin text-primary"}),r.jsx("p",{className:"text-sm text-muted-foreground animate-pulse",children:"Loading your resumes..."})]})}):(0,r.jsxs)(n.r,{children:[r.jsx(l.x,{heading:"Resumes",text:"Create and manage your professional resumes",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(o.z,{variant:"outline",size:"icon",onClick:T,disabled:C,className:"h-9 w-9",children:[r.jsx(m.Z,{className:`h-4 w-4 ${C?"animate-spin":""}`}),r.jsx("span",{className:"sr-only",children:"Refresh"})]}),(0,r.jsxs)(o.z,{onClick:R,className:"gap-1",children:[r.jsx(p.Z,{className:"mr-1 h-4 w-4"}),"New Resume"]})]})}),r.jsx("div",{className:"mt-8",children:r.jsx(b,{count:w.length,isLoading:N})}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:justify-between mt-8",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-96",children:[r.jsx(j.I,{placeholder:"Search resumes...",value:D,onChange:e=>q(e.target.value),className:"pl-10"}),r.jsx(c.Z,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,r.jsxs)(v.Ph,{value:S,onValueChange:e=>{console.log("Changing sort order to:",e),_(e)},children:[r.jsx(v.i4,{className:"w-[180px]",children:r.jsx(v.ki,{placeholder:"Sort order"})}),(0,r.jsxs)(v.Bw,{children:[r.jsx(v.Ql,{value:"newest",children:"Newest first"}),r.jsx(v.Ql,{value:"oldest",children:"Oldest first"}),r.jsx(v.Ql,{value:"a-z",children:"A to Z"}),r.jsx(v.Ql,{value:"z-a",children:"Z to A"})]})]})]})]}),r.jsx("div",{className:"mt-8",children:r.jsx(d.R,{type:"resume",documents:Z,loading:N,onCreateNew:R,onDelete:k,onRefresh:T,hideSearch:!0,hideSort:!0,externalSearchQuery:D})}),r.jsx("div",{className:"mt-6",children:r.jsx(x.g,{open:s,onOpenChange:f,documentType:"resume",onDocumentCreated:()=>{T(),f(!1),I({title:"Resume created",description:"Your resume has been created successfully",key:"resume-created-"+new Date().getTime()})}})})]})}},3791:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>a});var r=s(5347);let a=(0,r.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\resumes\page.tsx#dynamic`),i=(0,r.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\resumes\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[475,97,12,166,43,967,445,223],()=>s(9793));module.exports=r})();