(()=>{var e={};e.id=279,e.ids=[279],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},5553:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(1266),r(2626),r(546);var a=r(170),o=r(5002),n=r(3876),s=r.n(n),i=r(6299),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["(dashboard)",{children:["document",{children:["[type]",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1266)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\document\\[type]\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\document\\[type]\\[id]\\page.tsx"],u="/(dashboard)/document/[type]/[id]/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/(dashboard)/document/[type]/[id]/page",pathname:"/document/[type]/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5558:(e,t,r)=>{Promise.resolve().then(r.bind(r,6297))},6297:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var a=r(7247),o=r(8964),n=r(4178),s=r(9767),i=r(5503),l=r(8053),d=r(7757),c=r(4662),u=r(1897),p=r(6323);let m=(0,p.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var f=r(8917),x=r(999);let h=(0,p.Z)("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var g=r(4445),v=r(906),y=r(8969),b=r(6991),w=r(2394),N=r(8749);function j({open:e,onOpenChange:t,documentType:r,document:n,onDocumentUpdated:s}){let[i,d]=(0,o.useState)(""),[c,u]=(0,o.useState)(!1),{toast:p}=(0,v.pm)(),m=async()=>{if(!i.trim()){p({title:"Title required",description:"Please enter a title for your document",variant:"destructive"});return}u(!0);try{"resume"===r?await g.md.updateResume(n.id,{title:i}):"coverLetter"===r?await g.Gi.updateCoverLetter(n.id,{title:i}):"linkedin"===r&&await g.bc.updateLinkedInBio(n.id,{title:i}),p({title:"Document updated",description:"Your document has been updated successfully"}),s()}catch(e){p({title:"Error updating document",description:"There was an error updating your document. Please try again.",variant:"destructive"})}finally{u(!1)}};return a.jsx(y.Vq,{open:e,onOpenChange:t,children:(0,a.jsxs)(y.cZ,{className:"sm:max-w-[500px]",children:[(0,a.jsxs)(y.fK,{children:[a.jsx(y.$N,{children:(()=>{switch(r){case"resume":return"Edit Resume";case"coverLetter":return"Edit Cover Letter";case"linkedin":return"Edit LinkedIn Bio";default:return"Edit Document"}})()}),a.jsx(y.Be,{children:"Update your document information"})]}),a.jsx("div",{className:"space-y-4 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(w._,{htmlFor:"title",children:"Document Title"}),a.jsx(b.I,{id:"title",value:i,onChange:e=>d(e.target.value),placeholder:"Enter document title"})]})}),(0,a.jsxs)(y.cN,{children:[a.jsx(l.z,{variant:"outline",onClick:()=>t(!1),disabled:c,children:"Cancel"}),a.jsx(l.z,{type:"submit",onClick:m,disabled:c,children:c?(0,a.jsxs)(a.Fragment,{children:[a.jsx(N.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Save Changes"})]})]})})}let k=(e,t,r)=>{try{let a=new Blob([e],{type:r}),o=window.URL.createObjectURL(a),n=document.createElement("a");return n.href=o,n.setAttribute("download",t),document.body.appendChild(n),n.click(),n.remove(),window.URL.revokeObjectURL(o),!0}catch(e){return console.error("Error downloading file:",e),!1}},D=(e,t)=>k(e,t.endsWith(".pdf")?t:`${t}.pdf`,"application/pdf"),C=(e,t)=>k(e,t.endsWith(".docx")?t:`${t}.docx`,"application/vnd.openxmlformats-officedocument.wordprocessingml.document"),E=(e,t)=>k(e,t.endsWith(".txt")?t:`${t}.txt`,"text/plain");function P(){let e=(0,n.useParams)(),t=(0,n.useRouter)(),{toast:r}=(0,v.pm)(),[p,y]=(0,o.useState)(null),[b,w]=(0,o.useState)(!0),[N,k]=(0,o.useState)(!1),[P,T]=(0,o.useState)("preview"),$=e.type,I=e.id,L="resume"===$?"resume":"cover-letter"===$?"coverLetter":"linkedin"===$?"linkedin":"resume";console.log("URL type parameter:",$),console.log("Converted to document type:",L);let R=async()=>{w(!0);try{let e;if(console.log(`Fetching document with type: ${$}, id: ${I}`),!/^[0-9a-fA-F]{24}$/.test(I))throw console.error("Invalid MongoDB ID format:",I),Error(`Invalid document ID format: ${I}`);if("resume"===$)e=await g.md.getResume(I),console.log("Resume response:",e);else if("cover-letter"===$){console.log("Fetching cover letter with ID:",I);try{e=await g.Gi.getCoverLetter(I),console.log("Cover letter response:",e)}catch(e){throw console.error("Error in cover letter API call:",e),Error(`Failed to fetch cover letter: ${e?.message||"Unknown error"}`)}}else if("linkedin"===$)e=await g.bc.getLinkedInBio(I),console.log("LinkedIn bio response:",e);else throw console.error("Unknown document type:",$),Error(`Unknown document type: ${$}`);let t=null;if(console.log("Response structure:",JSON.stringify(e?.data,null,2)),e?.data?.resume?t=e.data.resume:e?.data?.coverLetter?t=e.data.coverLetter:e?.data?.linkedinBio?t=e.data.linkedinBio:!e?.data||(t=e.data,"cover-letter"!==$||t.resultText||t.coverLetter||(t={coverLetter:t})),console.log("Document data after extraction:",t),!t)throw console.error("No document data found in response:",e),Error("No document data found in response");y(t)}catch(e){console.error("Error fetching document:",e),r({title:"Error fetching document",description:`Could not load the document: ${e?.message||"Unknown error"}. Please try again.`,variant:"destructive"}),setTimeout(()=>{t.push("/dashboard")},3e3)}finally{w(!1)}},F=async()=>{try{let e;if(!p||!p.title)throw Error("Document not loaded properly");if(console.log(`Exporting PDF for document type: ${$}, id: ${I}`),"resume"===$)console.log("Exporting resume PDF for ID:",I),e=await g.md.generateResumePdf(I);else if("cover-letter"===$)console.log("Exporting cover letter PDF for ID:",I),e=await g.Gi.generateCoverLetterPdf(I);else if("linkedin"===$)console.log("Exporting LinkedIn bio PDF for ID:",I),e=await g.bc.generateLinkedInBioPdf(I);else throw Error(`Unknown document type for PDF export: ${$}`);if(!e)throw Error("No response from server");if(console.log("PDF export response:",e),D(e.data,p.title))r({title:"PDF downloaded",description:"Your document has been downloaded as PDF"});else throw Error("Failed to download PDF")}catch(e){console.error("PDF export error:",e),e.response?.status===403?(r({title:"Export not available",description:"PDF export is only available for Basic and Premium plans. Please upgrade your plan.",variant:"destructive"}),setTimeout(()=>{t.push("/subscription")},3e3)):r({title:"Export failed",description:`Could not export the document to PDF: ${e?.message||"Unknown error"}. Please try again.`,variant:"destructive"})}},X=async()=>{try{let e;if(!p||!p.title)throw Error("Document not loaded properly");if(console.log(`Exporting DOCX for document type: ${$}, id: ${I}`),"resume"===$)console.log("Exporting resume DOCX for ID:",I),e=await g.md.exportResumeDocx(I);else if("cover-letter"===$)console.log("Exporting cover letter DOCX for ID:",I),e=await g.Gi.exportCoverLetterDocx(I);else if("linkedin"===$)console.log("Exporting LinkedIn bio DOCX for ID:",I),e=await g.bc.exportLinkedInBioDocx(I);else throw Error(`Unknown document type for DOCX export: ${$}`);if(!e)throw Error("No response from server");if(console.log("DOCX export response:",e),C(e.data,p.title))r({title:"DOCX downloaded",description:"Your document has been downloaded as DOCX"});else throw Error("Failed to download DOCX")}catch(e){console.error("DOCX export error:",e),e.response?.status===403?(r({title:"Export not available",description:"DOCX export is only available for Basic and Premium plans. Please upgrade your plan.",variant:"destructive"}),setTimeout(()=>{t.push("/subscription")},3e3)):r({title:"Export failed",description:`Could not export the document to DOCX: ${e?.message||"Unknown error"}. Please try again.`,variant:"destructive"})}},q=async()=>{try{let e;if(!p||!p.title)throw Error("Document not loaded properly");if(console.log(`Exporting TXT for document type: ${$}, id: ${I}`),"resume"===$)console.log("Exporting resume TXT for ID:",I),e=await g.md.exportResumeTxt(I);else if("cover-letter"===$)console.log("Exporting cover letter TXT for ID:",I),e=await g.Gi.exportCoverLetterTxt(I);else if("linkedin"===$)console.log("Exporting LinkedIn bio TXT for ID:",I),e=await g.bc.exportLinkedInBioTxt(I);else throw Error(`Unknown document type for TXT export: ${$}`);if(!e)throw Error("No response from server");if(console.log("TXT export response:",e),E(e.data,p.title))r({title:"TXT downloaded",description:"Your document has been downloaded as TXT"});else throw Error("Failed to download TXT")}catch(e){console.error("TXT export error:",e),e.response?.status===403?(r({title:"Export not available",description:"TXT export is only available for paid plans. Please upgrade your plan.",variant:"destructive"}),setTimeout(()=>{t.push("/subscription")},3e3)):r({title:"Export failed",description:`Could not export the document to TXT: ${e?.message||"Unknown error"}. Please try again.`,variant:"destructive"})}};return(0,a.jsxs)(s.r,{children:[a.jsx(i.x,{heading:b?"Loading...":p?.title||"Document",text:`View and export your ${(()=>{switch($){case"resume":return"Resume";case"cover-letter":return"Cover Letter";case"linkedin":return"LinkedIn Bio";default:return"Document"}})().toLowerCase()}`,className:"animate-fade-in",children:(0,a.jsxs)("div",{className:"flex space-x-2 animate-fade-in [animation-delay:200ms]",children:[(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>t.push("/dashboard"),className:"transition-all duration-300 hover:bg-primary/10 hover:border-primary/30",children:[a.jsx(m,{className:"mr-2 h-4 w-4 transition-transform duration-300 group-hover:-translate-x-1"}),"Back"]}),(0,a.jsxs)(l.z,{variant:"outline",onClick:()=>k(!0),className:"transition-all duration-300 hover:bg-primary/10 hover:border-primary/30",children:[a.jsx(f.Z,{className:"mr-2 h-4 w-4 transition-transform duration-300 group-hover:rotate-12"}),"Edit"]})]})}),b?(0,a.jsxs)("div",{className:"space-y-4 animate-pulse",children:[a.jsx(u.O,{className:"h-8 w-[250px] animate-pulse"}),a.jsx(u.O,{className:"h-[300px] w-full animate-pulse"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.mQ,{value:P,onValueChange:T,className:"space-y-4",children:[a.jsx("div",{className:"flex items-center justify-between",children:(0,a.jsxs)(c.dr,{className:"animate-fade-in",children:[a.jsx(c.SP,{value:"preview",className:"transition-all duration-300 hover:bg-primary/10",children:"Preview"}),a.jsx(c.SP,{value:"export",className:"transition-all duration-300 hover:bg-primary/10",children:"Export"})]})}),a.jsx(c.nU,{value:"preview",className:"space-y-4 animate-fade-in",children:a.jsx(d.Zb,{className:"transition-all duration-300 hover:shadow-md",children:a.jsx(d.aY,{className:"pt-6",children:a.jsx("div",{className:"prose max-w-none dark:prose-invert",children:a.jsx("div",{dangerouslySetInnerHTML:{__html:(()=>{if(!p)return"";if(console.log("Getting content from document:",p),"string"==typeof p.content)return p.content.replace(/\n/g,"<br>");if(p.resultText)return p.resultText.replace(/\n/g,"<br>");if("linkedin"===$){if(p.profile&&p.content){let{profile:e,experience:t,content:r}=p,a="";return a+=`<h1>${e.firstName} ${e.lastName}</h1>`,r.headline&&"string"==typeof r.headline?a+=`<h2>${r.headline}</h2>`:e.headline?a+=`<h2>${e.headline}</h2>`:e.currentPosition&&(a+=`<h2>${e.currentPosition}</h2>`),e.location&&e.industry&&(a+=`<p>${e.location} | ${e.industry}</p>`),r.about&&"string"==typeof r.about&&(a+=`<h3>About</h3><p>${r.about.replace(/\n/g,"<br>")}</p>`),r.experience&&"string"==typeof r.experience?a+=`<h3>Experience</h3><p>${r.experience.replace(/\n/g,"<br>")}</p>`:t&&t.professionalExperience&&(a+=`<h3>Experience</h3><p>${t.professionalExperience.replace(/\n/g,"<br>")}</p>`),t&&t.skills&&(a+=`<h3>Skills</h3><p>${t.skills.replace(/\n/g,"<br>")}</p>`),a}if(p.profile){let{profile:e,experience:t}=p;return`
          <h1>${e.firstName} ${e.lastName}</h1>
          <h2>${e.headline||e.currentPosition}</h2>
          <p>${e.location} | ${e.industry}</p>
          ${t?`
            <h3>Experience</h3>
            <p>${t.professionalExperience?.replace(/\n/g,"<br>")||""}</p>
            <h3>Skills</h3>
            <p>${t.skills?.replace(/\n/g,"<br>")||""}</p>
          `:""}
        `}if(p.content&&"object"==typeof p.content){let e=p.content,t='<div class="linkedin-content">';return e.headline&&(t+=`<h3>Headline</h3><p>${e.headline}</p>`),e.about&&(t+=`<h3>About</h3><p>${e.about.replace(/\n/g,"<br>")}</p>`),e.experience&&(t+=`<h3>Experience</h3><p>${e.experience.replace(/\n/g,"<br>")}</p>`),t+="</div>"}}if("cover-letter"===$){if(p.resultText)return p.resultText.replace(/\n/g,"<br>");if(p.coverLetter&&p.coverLetter.resultText)return p.coverLetter.resultText.replace(/\n/g,"<br>")}try{return`<pre>${JSON.stringify(p,null,2)}</pre>`}catch(e){return"No content available"}})()}})})})})}),a.jsx(c.nU,{value:"export",className:"space-y-4 animate-fade-in",children:a.jsx(d.Zb,{className:"transition-all duration-300 hover:shadow-md",children:a.jsx(d.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)(l.z,{onClick:F,className:"flex h-24 flex-col items-center justify-center gap-2 transition-all duration-300 hover:scale-105",children:[a.jsx(x.Z,{className:"h-8 w-8 transition-transform duration-300 group-hover:scale-110"}),a.jsx("span",{children:"Export as PDF"})]}),(0,a.jsxs)(l.z,{onClick:X,className:"flex h-24 flex-col items-center justify-center gap-2 transition-all duration-300 hover:scale-105",children:[a.jsx(h,{className:"h-8 w-8 transition-transform duration-300 group-hover:scale-110"}),a.jsx("span",{children:"Export as DOCX"})]}),(0,a.jsxs)(l.z,{onClick:q,variant:"outline",className:"flex h-24 flex-col items-center justify-center gap-2 transition-all duration-300 hover:scale-105",children:[a.jsx(h,{className:"h-8 w-8 transition-transform duration-300 group-hover:scale-110"}),a.jsx("span",{children:"Export as TXT"})]})]})})})})]}),p&&a.jsx(j,{open:N,onOpenChange:k,documentType:L,document:p,onDocumentUpdated:()=>{R(),k(!1)},className:"animate-fade-in"})]})]})}},7757:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>d,Zb:()=>s,aY:()=>c,eW:()=>u,ll:()=>l});var a=r(7247),o=r(8964),n=r(5008);let s=o.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md",e),...t}));s.displayName="Card";let i=o.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=o.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=o.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=o.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=o.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},8969:(e,t,r)=>{"use strict";r.d(t,{$N:()=>f,Be:()=>x,Vq:()=>l,cN:()=>m,cZ:()=>u,fK:()=>p});var a=r(7247),o=r(8964),n=r(400),s=r(7013),i=r(5008);let l=n.fC;n.xz;let d=n.h_;n.x8;let c=o.forwardRef(({className:e,...t},r)=>a.jsx(n.aV,{ref:r,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));c.displayName=n.aV.displayName;let u=o.forwardRef(({className:e,children:t,...r},o)=>(0,a.jsxs)(d,{children:[a.jsx(c,{}),(0,a.jsxs)(n.VY,{ref:o,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,(0,a.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(s.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=n.VY.displayName;let p=({className:e,...t})=>a.jsx("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let m=({className:e,...t})=>a.jsx("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});m.displayName="DialogFooter";let f=o.forwardRef(({className:e,...t},r)=>a.jsx(n.Dx,{ref:r,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));f.displayName=n.Dx.displayName;let x=o.forwardRef(({className:e,...t},r)=>a.jsx(n.dk,{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=n.dk.displayName},6991:(e,t,r)=>{"use strict";r.d(t,{I:()=>s});var a=r(7247),o=r(8964),n=r(5008);let s=o.forwardRef(({className:e,type:t,...r},o)=>a.jsx("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:o,...r}));s.displayName="Input"},2394:(e,t,r)=>{"use strict";r.d(t,{_:()=>d});var a=r(7247),o=r(8964),n=r(768),s=r(7972),i=r(5008);let l=(0,s.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef(({className:e,...t},r)=>a.jsx(n.f,{ref:r,className:(0,i.cn)(l(),e),...t}));d.displayName=n.f.displayName},1897:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});var a=r(7247),o=r(5008);function n({className:e,...t}){return a.jsx("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...t})}},4662:(e,t,r)=>{"use strict";r.d(t,{SP:()=>d,dr:()=>l,mQ:()=>i,nU:()=>c});var a=r(7247),o=r(8964),n=r(8754),s=r(5008);let i=n.fC,l=o.forwardRef(({className:e,...t},r)=>a.jsx(n.aV,{ref:r,className:(0,s.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=n.aV.displayName;let d=o.forwardRef(({className:e,...t},r)=>a.jsx(n.xz,{ref:r,className:(0,s.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=n.xz.displayName;let c=o.forwardRef(({className:e,...t},r)=>a.jsx(n.VY,{ref:r,className:(0,s.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=n.VY.displayName},999:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(6323).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},8917:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(6323).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},1266:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(5347).createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\document\[type]\[id]\page.tsx#default`)},768:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var a=r(8964),o=r(2251),n=r(7247),s=a.forwardRef((e,t)=>(0,n.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var i=s},8754:(e,t,r)=>{"use strict";r.d(t,{VY:()=>I,aV:()=>T,fC:()=>P,xz:()=>$});var a=r(8964),o=r(319),n=r(732),s=r(5706),i=r(7264),l=r(2251),d=r(1310),c=r(8469),u=r(7015),p=r(7247),m="Tabs",[f,x]=(0,n.b)(m,[s.Pc]),h=(0,s.Pc)(),[g,v]=f(m),y=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:o,defaultValue:n,orientation:s="horizontal",dir:i,activationMode:m="automatic",...f}=e,x=(0,d.gm)(i),[h,v]=(0,c.T)({prop:a,onChange:o,defaultProp:n});return(0,p.jsx)(g,{scope:r,baseId:(0,u.M)(),value:h,onValueChange:v,orientation:s,dir:x,activationMode:m,children:(0,p.jsx)(l.WV.div,{dir:x,"data-orientation":s,...f,ref:t})})});y.displayName=m;var b="TabsList",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...o}=e,n=v(b,r),i=h(r);return(0,p.jsx)(s.fC,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:a,children:(0,p.jsx)(l.WV.div,{role:"tablist","aria-orientation":n.orientation,...o,ref:t})})});w.displayName=b;var N="TabsTrigger",j=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...i}=e,d=v(N,r),c=h(r),u=C(d.baseId,a),m=E(d.baseId,a),f=a===d.value;return(0,p.jsx)(s.ck,{asChild:!0,...c,focusable:!n,active:f,children:(0,p.jsx)(l.WV.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":m,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...i,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;f||n||!e||d.onValueChange(a)})})})});j.displayName=N;var k="TabsContent",D=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:n,children:s,...d}=e,c=v(k,r),u=C(c.baseId,o),m=E(c.baseId,o),f=o===c.value,x=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.z,{present:n||f,children:({present:r})=>(0,p.jsx)(l.WV.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:r&&s})})});function C(e,t){return`${e}-trigger-${t}`}function E(e,t){return`${e}-content-${t}`}D.displayName=k;var P=y,T=w,$=j,I=D}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[475,97,12,43,967],()=>r(5553));module.exports=a})();