const docx = require("docx");
const {
  Document,
  Paragraph,
  TextRun,
  HeadingLevel,
  AlignmentType,
  Table,
  TableRow,
  TableCell,
  WidthType,
  BorderStyle,
} = docx;
const fs = require("fs");
const path = require("path");

/**
 * Generate DOCX document
 * @param {Object} data - Document data
 * @param {string} documentType - Type of document (resume, coverLetter, linkedin)
 * @param {string} title - Document title
 * @returns {Promise<Buffer>} - DOCX buffer
 */
exports.generateDocx = async (data, documentType, title) => {
  try {
    let doc;

    // Generate document based on type
    switch (documentType) {
      case "resume":
        doc = generateResumeDocx(data, title);
        break;
      case "coverLetter":
        doc = generateCoverLetterDocx(data, title);
        break;
      case "linkedin":
        doc = generateLinkedInDocx(data, title);
        break;
      default:
        doc = generateGenericDocx(data, title);
    }

    // Create a buffer with the document
    const buffer = await doc.save();
    return buffer;
  } catch (error) {
    console.error("DOCX Generation Error:", error);
    throw new Error("Failed to generate DOCX document");
  }
};

/**
 * Generate a resume DOCX document
 * @param {Object} data - Resume data
 * @param {string} title - Document title
 * @returns {Document} - DOCX document
 */
function generateResumeDocx(data, title) {
  console.log(
    "Generating resume DOCX with data:",
    JSON.stringify(data, null, 2)
  );

  // Check if we have resultText (AI-generated content)
  if (data.resultText) {
    return generateSimpleDocxFromMarkdown(data.resultText, title);
  }

  // Otherwise, use the structured data approach
  const { personal, experience, education, skills } = data;

  // Create document
  const doc = new Document({
    title,
    description: "Resume generated by CareerPilotAI",
    styles: {
      paragraphStyles: [
        {
          id: "Heading1",
          name: "Heading 1",
          basedOn: "Normal",
          next: "Normal",
          quickFormat: true,
          run: {
            size: 28,
            bold: true,
            color: "2C3E50",
          },
          paragraph: {
            spacing: {
              after: 120,
            },
          },
        },
        {
          id: "Heading2",
          name: "Heading 2",
          basedOn: "Normal",
          next: "Normal",
          quickFormat: true,
          run: {
            size: 24,
            bold: true,
            color: "3498DB",
          },
          paragraph: {
            spacing: {
              before: 240,
              after: 120,
            },
          },
        },
      ],
    },
  });

  // Add sections
  const sections = [];

  // Header with name and contact info
  sections.push(
    new Paragraph({
      text: `${personal.firstName} ${personal.lastName}`,
      heading: HeadingLevel.HEADING_1,
      alignment: AlignmentType.CENTER,
    })
  );

  // Contact info (simplified)
  sections.push(
    new Paragraph({
      alignment: AlignmentType.CENTER,
      children: [new TextRun(`${personal.email || "<EMAIL>"}`)],
    })
  );

  // Summary
  if (personal.summary) {
    sections.push(
      new Paragraph({
        text: "SUMMARY",
        heading: HeadingLevel.HEADING_2,
      })
    );

    sections.push(
      new Paragraph({
        text: personal.summary,
      })
    );
  }

  // Experience
  sections.push(
    new Paragraph({
      text: "EXPERIENCE",
      heading: HeadingLevel.HEADING_2,
    })
  );

  // Handle both array and string experience formats
  if (Array.isArray(experience)) {
    experience.forEach((job) => {
      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: job.position || "Position",
              bold: true,
            }),
            new TextRun(" at "),
            new TextRun({
              text: job.company || "Company",
              bold: true,
            }),
          ],
        })
      );

      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `${job.startDate || "Start Date"} - ${
                job.endDate || "Present"
              }`,
              italics: true,
            }),
          ],
        })
      );

      sections.push(
        new Paragraph({
          text: job.description || "",
          spacing: {
            after: 200,
          },
        })
      );
    });
  } else if (typeof experience === "string") {
    // If experience is a string, just add it as a paragraph
    sections.push(
      new Paragraph({
        text: experience,
        spacing: {
          after: 200,
        },
      })
    );
  }

  // Education
  sections.push(
    new Paragraph({
      text: "EDUCATION",
      heading: HeadingLevel.HEADING_2,
    })
  );

  // Handle both array and string education formats
  if (Array.isArray(education)) {
    education.forEach((edu) => {
      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: edu.degree || "Degree",
              bold: true,
            }),
            new TextRun(" in "),
            new TextRun({
              text: edu.field || "Field",
              bold: true,
            }),
          ],
        })
      );

      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: edu.institution || "Institution",
              bold: true,
            }),
          ],
        })
      );

      sections.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `${edu.startDate || "Start Date"} - ${
                edu.endDate || "End Date"
              }`,
              italics: true,
            }),
          ],
          spacing: {
            after: 200,
          },
        })
      );
    });
  } else if (typeof education === "string") {
    // If education is a string, just add it as a paragraph
    sections.push(
      new Paragraph({
        text: education,
        spacing: {
          after: 200,
        },
      })
    );
  }

  // Skills
  sections.push(
    new Paragraph({
      text: "SKILLS",
      heading: HeadingLevel.HEADING_2,
    })
  );

  // Handle both array of objects and array of strings for skills
  let skillsText = "";
  if (Array.isArray(skills)) {
    if (skills.length > 0 && typeof skills[0] === "object") {
      skillsText = skills.map((skill) => skill.name).join(", ");
    } else {
      skillsText = skills.join(", ");
    }
  } else if (typeof skills === "string") {
    skillsText = skills;
  }

  sections.push(
    new Paragraph({
      text: skillsText,
    })
  );

  // Add all sections to document
  doc.addSection({
    properties: {},
    children: sections,
  });

  return doc;
}

/**
 * Generate a simple DOCX document from markdown text
 * @param {string} markdownText - Markdown text content
 * @param {string} title - Document title
 * @returns {Document} - DOCX document
 */
function generateSimpleDocxFromMarkdown(markdownText, title) {
  console.log("Generating simple DOCX from markdown");

  try {
    // Create document with minimal configuration
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              text: title,
              heading: HeadingLevel.HEADING_1,
              alignment: AlignmentType.CENTER,
            }),
          ],
        },
      ],
    });

    // Split by lines and process
    const lines = markdownText.split("\n");
    const paragraphs = [];

    for (const line of lines) {
      // Handle headings
      if (line.startsWith("# ")) {
        paragraphs.push(
          new Paragraph({
            text: line.substring(2),
            heading: HeadingLevel.HEADING_1,
          })
        );
      } else if (line.startsWith("## ")) {
        paragraphs.push(
          new Paragraph({
            text: line.substring(3),
            heading: HeadingLevel.HEADING_2,
          })
        );
      } else if (line.trim() !== "") {
        // Regular line
        paragraphs.push(
          new Paragraph({
            text: line,
          })
        );
      }
    }

    // Add all paragraphs to document
    if (paragraphs.length > 0) {
      doc.addSection({
        properties: {},
        children: paragraphs,
      });
    }

    return doc;
  } catch (error) {
    console.error("Error in generateSimpleDocxFromMarkdown:", error);

    // Fallback to a very simple document
    const fallbackDoc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              text: title,
              heading: HeadingLevel.HEADING_1,
            }),
            new Paragraph({
              text: "Resume content could not be formatted properly.",
            }),
            new Paragraph({
              text: markdownText.substring(0, 1000), // Limit text length
            }),
          ],
        },
      ],
    });

    return fallbackDoc;
  }
}

/**
 * Generate a cover letter DOCX document
 * @param {Object} data - Cover letter data
 * @param {string} title - Document title
 * @returns {Document} - DOCX document
 */
function generateCoverLetterDocx(data, title) {
  const { job, personal, content } = data;

  // Create document
  const doc = new Document({
    title,
    description: "Cover Letter generated by CareerPilotAI",
  });

  // Add sections
  const sections = [];

  // Header with date
  sections.push(
    new Paragraph({
      text: new Date().toLocaleDateString(),
      alignment: AlignmentType.RIGHT,
    })
  );

  // Spacing
  sections.push(new Paragraph({}));

  // Recipient info
  if (job.hiringManager) {
    sections.push(
      new Paragraph({
        text: job.hiringManager,
      })
    );
  }

  sections.push(
    new Paragraph({
      text: job.company,
    })
  );

  // Spacing
  sections.push(new Paragraph({}));

  // Greeting
  sections.push(
    new Paragraph({
      text: job.hiringManager
        ? `Dear ${job.hiringManager},`
        : "Dear Hiring Manager,",
    })
  );

  // Content
  const contentParagraphs = content.split("\n\n");
  contentParagraphs.forEach((paragraph) => {
    sections.push(
      new Paragraph({
        text: paragraph,
      })
    );
  });

  // Closing
  sections.push(
    new Paragraph({
      text: "Sincerely,",
      spacing: {
        before: 400,
      },
    })
  );

  sections.push(
    new Paragraph({
      text: `${personal.firstName} ${personal.lastName}`,
      spacing: {
        before: 200,
      },
    })
  );

  // Add all sections to document
  doc.addSection({
    properties: {},
    children: sections,
  });

  return doc;
}

/**
 * Generate a LinkedIn profile DOCX document
 * @param {Object} data - LinkedIn data
 * @param {string} title - Document title
 * @returns {Document} - DOCX document
 */
function generateLinkedInDocx(data, title) {
  const { profile, content } = data;

  // Create document
  const doc = new Document({
    title,
    description: "LinkedIn Profile generated by CareerPilotAI",
  });

  // Add sections
  const sections = [];

  // Header with name
  sections.push(
    new Paragraph({
      text: `${profile.firstName} ${profile.lastName}`,
      heading: HeadingLevel.HEADING_1,
      alignment: AlignmentType.CENTER,
    })
  );

  // Headline
  sections.push(
    new Paragraph({
      text: content.headline || profile.headline,
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 200,
      },
    })
  );

  // Location and Industry
  sections.push(
    new Paragraph({
      text: `${profile.location} | ${profile.industry}`,
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 400,
      },
    })
  );

  // About section
  sections.push(
    new Paragraph({
      text: "About",
      heading: HeadingLevel.HEADING_2,
    })
  );

  const aboutParagraphs = content.about.split("\n\n");
  aboutParagraphs.forEach((paragraph) => {
    sections.push(
      new Paragraph({
        text: paragraph,
      })
    );
  });

  // Experience section
  sections.push(
    new Paragraph({
      text: "Experience",
      heading: HeadingLevel.HEADING_2,
      spacing: {
        before: 400,
      },
    })
  );

  sections.push(
    new Paragraph({
      text: profile.currentPosition,
      bold: true,
    })
  );

  const experienceParagraphs = content.experience.split("\n\n");
  experienceParagraphs.forEach((paragraph) => {
    sections.push(
      new Paragraph({
        text: paragraph,
      })
    );
  });

  // Add all sections to document
  doc.addSection({
    properties: {},
    children: sections,
  });

  return doc;
}

/**
 * Generate a generic DOCX document
 * @param {string} content - Document content
 * @param {string} title - Document title
 * @returns {Document} - DOCX document
 */
function generateGenericDocx(content, title) {
  // Create document
  const doc = new Document({
    title,
    description: "Document generated by CareerPilotAI",
  });

  // Add title and content
  const children = [
    new Paragraph({
      text: title,
      heading: HeadingLevel.HEADING_1,
    }),
  ];

  // Split content into paragraphs
  const paragraphs = content.split("\n\n");
  paragraphs.forEach((paragraph) => {
    children.push(
      new Paragraph({
        text: paragraph,
      })
    );
  });

  // Add all content to document
  doc.addSection({
    properties: {},
    children,
  });

  return doc;
}
