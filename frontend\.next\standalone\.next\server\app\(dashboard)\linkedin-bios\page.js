(()=>{var e={};e.id=873,e.ids=[873],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},5059:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l}),s(2614),s(2626),s(546);var r=s(170),i=s(5002),a=s(3876),n=s.n(a),d=s(6299),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let l=["",{children:["(dashboard)",{children:["linkedin-bios",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2614)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\linkedin-bios\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\linkedin-bios\\page.tsx"],u="/(dashboard)/linkedin-bios/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/(dashboard)/linkedin-bios/page",pathname:"/linkedin-bios",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1534:(e,t,s)=>{Promise.resolve().then(s.bind(s,7806))},7806:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k,dynamic:()=>v});var r=s(7247),i=s(8964),a=s(7734),n=s(9767),d=s(5503),o=s(223),l=s(8053),c=s(6903),u=s(8749),p=s(8339),x=s(4615),m=s(5497),h=s(4445),g=s(906),f=s(7757),b=s(6991),j=s(4049);let v="force-dynamic";function w({count:e,isLoading:t}){return(0,r.jsxs)(f.Zb,{className:"overflow-hidden border-muted-foreground/20",children:[(0,r.jsxs)(f.Ol,{className:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/50 dark:to-indigo-900/30 pb-2",children:[(0,r.jsxs)(f.ll,{className:"flex items-center gap-2 text-indigo-700 dark:text-indigo-300",children:[r.jsx(c.Z,{className:"h-5 w-5"}),"LinkedIn Bios"]}),r.jsx(f.SZ,{children:"Your professional profiles"})]}),r.jsx(f.aY,{className:"pt-4",children:t?r.jsx("div",{className:"h-8 w-16 animate-pulse rounded-md bg-muted"}):r.jsx("div",{className:"text-2xl font-bold",children:e})})]})}function k(){let{isAuthenticated:e,loading:t}=(0,a.a)(),[s,f]=(0,i.useState)(!1),[v,k]=(0,i.useState)([]),[y,N]=(0,i.useState)(!0),[I,A]=(0,i.useState)(!1),[C,P]=(0,i.useState)(""),[L,D]=(0,i.useState)("newest"),{toast:q}=(0,g.pm)(),S=async()=>{N(!0),A(!0);try{let e=await h.bc.getAllLinkedInBios(),t=e.data.linkedinBios||e.data.data||[];k(t)}catch(e){console.error("Error fetching LinkedIn bios:",e),q({title:"Error fetching LinkedIn bios",description:"Please try again later",variant:"destructive"})}finally{N(!1),A(!1)}},_=()=>{f(!0)},T=async e=>{try{await h.bc.deleteLinkedInBio(e),k(v.filter(t=>t.id!==e)),q({title:"LinkedIn bio deleted",description:"Your LinkedIn bio has been deleted successfully"})}catch(e){q({title:"Error deleting LinkedIn bio",description:"Please try again later",variant:"destructive"})}},Z=[...v.filter(e=>e.title.toLowerCase().includes(C.toLowerCase())||e.headline&&e.headline.toLowerCase().includes(C.toLowerCase())||e.industry&&e.industry.toLowerCase().includes(C.toLowerCase()))].sort((e,t)=>{try{switch(L){case"newest":let s=e.updatedAt?new Date(e.updatedAt).getTime():new Date(e.createdAt).getTime();return(t.updatedAt?new Date(t.updatedAt).getTime():new Date(t.createdAt).getTime())-s;case"oldest":let r=e.updatedAt?new Date(e.updatedAt).getTime():new Date(e.createdAt).getTime(),i=t.updatedAt?new Date(t.updatedAt).getTime():new Date(t.createdAt).getTime();return r-i;case"a-z":return(e.title||"").localeCompare(t.title||"");case"z-a":return(t.title||"").localeCompare(e.title||"");default:return 0}}catch(e){return console.error("Error sorting LinkedIn bios:",e),0}});return t?r.jsx("div",{className:"flex h-screen items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[r.jsx(u.Z,{className:"h-10 w-10 animate-spin text-primary"}),r.jsx("p",{className:"text-sm text-muted-foreground animate-pulse",children:"Loading your LinkedIn bios..."})]})}):(0,r.jsxs)(n.r,{children:[r.jsx(d.x,{heading:"LinkedIn Bios",text:"Create and manage your professional LinkedIn profiles",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(l.z,{variant:"outline",size:"icon",onClick:S,disabled:I,className:"h-9 w-9",children:[r.jsx(p.Z,{className:`h-4 w-4 ${I?"animate-spin":""}`}),r.jsx("span",{className:"sr-only",children:"Refresh"})]}),(0,r.jsxs)(l.z,{onClick:_,className:"gap-1",children:[r.jsx(x.Z,{className:"mr-1 h-4 w-4"}),"New LinkedIn Bio"]})]})}),r.jsx("div",{className:"mt-8",children:r.jsx(w,{count:v.length,isLoading:y})}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:justify-between mt-8",children:[(0,r.jsxs)("div",{className:"relative w-full md:w-96",children:[r.jsx(b.I,{placeholder:"Search LinkedIn bios...",value:C,onChange:e=>P(e.target.value),className:"pl-10"}),r.jsx(c.Z,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,r.jsxs)(j.Ph,{value:L,onValueChange:e=>{console.log("Changing sort order to:",e),D(e)},children:[r.jsx(j.i4,{className:"w-[180px]",children:r.jsx(j.ki,{placeholder:"Sort order"})}),(0,r.jsxs)(j.Bw,{children:[r.jsx(j.Ql,{value:"newest",children:"Newest first"}),r.jsx(j.Ql,{value:"oldest",children:"Oldest first"}),r.jsx(j.Ql,{value:"a-z",children:"A to Z"}),r.jsx(j.Ql,{value:"z-a",children:"Z to A"})]})]})]})]}),r.jsx("div",{className:"mt-8",children:r.jsx(o.R,{type:"linkedin",documents:Z,loading:y,onCreateNew:_,onDelete:T,onRefresh:S,hideSearch:!0,hideSort:!0,externalSearchQuery:C})}),r.jsx("div",{className:"mt-6",children:r.jsx(m.g,{open:s,onOpenChange:f,documentType:"linkedin",onDocumentCreated:()=>{S(),f(!1),q({title:"LinkedIn bio created",description:"Your LinkedIn bio has been created successfully",key:"linkedin-bio-created-"+new Date().getTime()})}})})]})}},2614:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a,dynamic:()=>i});var r=s(5347);let i=(0,r.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\linkedin-bios\page.tsx#dynamic`),a=(0,r.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\linkedin-bios\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[475,97,12,166,43,967,445,223],()=>s(5059));module.exports=r})();