(()=>{var e={};e.id=548,e.ids=[548],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},5900:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>p,routeModule:()=>x,tree:()=>l}),r(5268),r(2626),r(546);var s=r(170),a=r(5002),o=r(3876),i=r.n(o),n=r(6299),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l=["",{children:["(dashboard)",{children:["cover-letters-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5268)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\cover-letters-test\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],p=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\cover-letters-test\\page.tsx"],u="/(dashboard)/cover-letters-test/page",c={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/cover-letters-test/page",pathname:"/cover-letters-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},4747:(e,t,r)=>{Promise.resolve().then(r.bind(r,7709))},7709:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>i});var s=r(7247),a=r(9767),o=r(5503);let i="force-dynamic";function n(){return(0,s.jsxs)(a.r,{children:[s.jsx(o.x,{heading:"Cover Letters Test",text:"This is a test page for cover letters"}),s.jsx("div",{className:"mt-8",children:s.jsx("p",{children:"If you can see this page, the routing is working correctly."})})]})}},5268:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>a});var s=r(5347);let a=(0,s.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\cover-letters-test\page.tsx#dynamic`),o=(0,s.createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\cover-letters-test\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[475,97,12,43,967],()=>r(5900));module.exports=s})();