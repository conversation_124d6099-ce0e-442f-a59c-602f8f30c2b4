(()=>{var e={};e.id=115,e.ids=[115],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7790:e=>{"use strict";e.exports=require("assert")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},5240:e=>{"use strict";e.exports=require("https")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},811:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(7304),r(2626),r(546);var t=r(170),a=r(5002),i=r(3876),l=r.n(i),n=r(6299),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d=["",{children:["(dashboard)",{children:["subscription",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7304)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\subscription\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2626)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,546)),"D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\not-found.tsx"]}],c=["D:\\Dev Ideas\\CareerPilotAI-2\\frontend\\app\\(dashboard)\\subscription\\page.tsx"],u="/(dashboard)/subscription/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/subscription/page",pathname:"/subscription",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2285:(e,s,r)=>{Promise.resolve().then(r.bind(r,4960))},4960:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var t=r(7247),a=r(8964),i=r(9767),l=r(5503),n=r(7757),o=r(8053),d=r(1897),c=r(1365),u=r(8799),p=r(8749),x=r(4445),m=r(906);function f(){let[e,s]=(0,a.useState)([]),[r,f]=(0,a.useState)(null),[h,j]=(0,a.useState)(!0),[g,b]=(0,a.useState)(!1),{toast:v}=(0,m.pm)(),N=async e=>{b(!0);try{let s=await x.wm.createCheckoutSession({planType:e});window.location.href=s.data.url}catch(e){v({title:"Subscription failed",description:"Could not process your subscription request. Please try again.",variant:"destructive"}),b(!1)}};return(0,t.jsxs)(i.r,{children:[t.jsx(l.x,{heading:"Subscription Plans",text:"Choose the right plan for your career needs"}),h?t.jsx("div",{className:"grid gap-6 md:grid-cols-3",children:[1,2,3].map(e=>(0,t.jsxs)(n.Zb,{className:"flex flex-col",children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(d.O,{className:"h-5 w-[100px]"}),t.jsx(d.O,{className:"h-8 w-[120px]"})]}),(0,t.jsxs)(n.aY,{className:"flex-1 space-y-4",children:[t.jsx(d.O,{className:"h-4 w-full"}),t.jsx(d.O,{className:"h-4 w-full"}),t.jsx(d.O,{className:"h-4 w-full"})]}),t.jsx(n.eW,{children:t.jsx(d.O,{className:"h-10 w-full"})})]},e))}):t.jsx("div",{className:"grid gap-6 md:grid-cols-3",children:e.map(e=>{let s=r?.plan===e.name.toLowerCase();return(0,t.jsxs)(n.Zb,{className:"flex flex-col",children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.SZ,{children:s&&t.jsx(c.C,{children:"Current Plan"})}),t.jsx(n.ll,{children:e.name}),(0,t.jsxs)("div",{className:"text-3xl font-bold",children:[0===e.price?"0":`₹${e.price}`,t.jsx("span",{className:"text-sm font-normal text-muted-foreground",children:"/month"})]})]}),(0,t.jsxs)(n.aY,{className:"flex-1",children:[t.jsx("p",{className:"mb-4 text-sm text-muted-foreground",children:e.description}),t.jsx("ul",{className:"space-y-2 text-sm",children:e.features.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-center",children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4 text-primary"}),e]},s))})]}),t.jsx(n.eW,{children:t.jsx(o.z,{className:"w-full",variant:s?"outline":"default",disabled:s||g,onClick:()=>N(e.name.toLowerCase()),children:g?(0,t.jsxs)(t.Fragment,{children:[t.jsx(p.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):s?"Current Plan":"Subscribe"})})]},e.id)})})]})}},7757:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>n,SZ:()=>d,Zb:()=>l,aY:()=>c,eW:()=>u,ll:()=>o});var t=r(7247),a=r(8964),i=r(5008);let l=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200 hover:shadow-md",e),...s}));l.displayName="Card";let n=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},1897:(e,s,r)=>{"use strict";r.d(s,{O:()=>i});var t=r(7247),a=r(5008);function i({className:e,...s}){return t.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",e),...s})}},7304:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(5347).createProxy)(String.raw`D:\Dev Ideas\CareerPilotAI-2\frontend\app\(dashboard)\subscription\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[475,97,12,43,967],()=>r(811));module.exports=t})();